using System;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormPasses : Form
{
	private IContainer components;

	private SplitContainer splitContainer1;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPageUnknown;

	private ListBox listBoxPasses;

	private ByteViewer byteViewer = new ByteViewer();

	public FormPasses()
	{
		InitializeComponent();
	}

	private void listBoxPasses_SelectedIndexChanged(object sender, EventArgs e)
	{
		ItemPass itemPass = listBoxPasses.SelectedItem as ItemPass;
		tabPageUnknown.Controls.Add(byteViewer);
		byteViewer.Dock = DockStyle.Fill;
		byteViewer.SetBytes(itemPass.unknownBytes.ToArray());
	}

	private void FormPasses_Load(object sender, EventArgs e)
	{
		foreach (ItemPass value in ItemPass.Passes.Values)
		{
			listBoxPasses.Items.Add(value);
		}
		listBoxPasses.DisplayMember = "FullName";
		listBoxPasses.ValueMember = "ID";
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.splitContainer1 = new System.Windows.Forms.SplitContainer();
		this.listBoxPasses = new System.Windows.Forms.ListBox();
		this.tabControl1 = new System.Windows.Forms.TabControl();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.tabPageUnknown = new System.Windows.Forms.TabPage();
		((System.ComponentModel.ISupportInitialize)this.splitContainer1).BeginInit();
		this.splitContainer1.Panel1.SuspendLayout();
		this.splitContainer1.Panel2.SuspendLayout();
		this.splitContainer1.SuspendLayout();
		this.tabControl1.SuspendLayout();
		base.SuspendLayout();
		this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.splitContainer1.Location = new System.Drawing.Point(0, 0);
		this.splitContainer1.Name = "splitContainer1";
		this.splitContainer1.Panel1.Controls.Add(this.listBoxPasses);
		this.splitContainer1.Panel2.Controls.Add(this.tabControl1);
		this.splitContainer1.Size = new System.Drawing.Size(546, 311);
		this.splitContainer1.SplitterDistance = 182;
		this.splitContainer1.TabIndex = 0;
		this.listBoxPasses.Dock = System.Windows.Forms.DockStyle.Fill;
		this.listBoxPasses.FormattingEnabled = true;
		this.listBoxPasses.Location = new System.Drawing.Point(0, 0);
		this.listBoxPasses.Name = "listBoxPasses";
		this.listBoxPasses.Size = new System.Drawing.Size(182, 311);
		this.listBoxPasses.TabIndex = 0;
		this.listBoxPasses.SelectedIndexChanged += new System.EventHandler(listBoxPasses_SelectedIndexChanged);
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPageUnknown);
		this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tabControl1.Location = new System.Drawing.Point(0, 0);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new System.Drawing.Size(360, 311);
		this.tabControl1.TabIndex = 0;
		this.tabPage1.Location = new System.Drawing.Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(352, 285);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "tabPage1";
		this.tabPage1.UseVisualStyleBackColor = true;
		this.tabPageUnknown.Location = new System.Drawing.Point(4, 22);
		this.tabPageUnknown.Name = "tabPageUnknown";
		this.tabPageUnknown.Padding = new System.Windows.Forms.Padding(3);
		this.tabPageUnknown.Size = new System.Drawing.Size(352, 285);
		this.tabPageUnknown.TabIndex = 1;
		this.tabPageUnknown.Text = "Unknown bytes";
		this.tabPageUnknown.UseVisualStyleBackColor = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(546, 311);
		base.Controls.Add(this.splitContainer1);
		base.Name = "FormPasses";
		this.Text = "FormPasses";
		base.Load += new System.EventHandler(FormPasses_Load);
		this.splitContainer1.Panel1.ResumeLayout(false);
		this.splitContainer1.Panel2.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.splitContainer1).EndInit();
		this.splitContainer1.ResumeLayout(false);
		this.tabControl1.ResumeLayout(false);
		base.ResumeLayout(false);
	}
}
