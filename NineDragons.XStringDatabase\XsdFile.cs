using System;
using System.IO;
using System.Text;

namespace NineDragons.XStringDatabase;

public class XsdFile : Xsd
{
	public XsdFile()
	{
		BindEvents();
	}

	public XsdFile(string filename)
		: this()
	{
		base.Filename = filename;
	}

	public XsdFile(string filename, byte[] keys)
		: this()
	{
		base.Filename = filename;
		base.Keys = keys;
	}

	public XsdFile(byte[] keys)
		: this()
	{
		base.Keys = keys;
	}

	public XsdFile(Version version)
		: base(version)
	{
		BindEvents();
	}

	public override void load(string filename)
	{
		if (!File.Exists(filename))
		{
			throw new FileNotFoundException("File '" + filename + "' does not exist.");
		}
		base.isEncrypted = false;
		base.Filename = filename;
		using (BinaryReader binaryReader = new BinaryReader(File.Open(filename, FileMode.Open)))
		{
			header = binaryReader.ReadInt32();
			if (!Xsd.ValidHeaders.Contains(header))
			{
				throw new Exception(string.Format("File '{0}' is not valid. [{1}]", filename, header.ToString("X4")));
			}
			version = binaryReader.ReadInt32();
			setupLanguages();
			base.totalSectionCount = binaryReader.ReadInt32();
			int i = 0;
			for (int num = base.totalSectionCount; i < num; i++)
			{
				Section section = new Section();
				section.XStringCount = binaryReader.ReadInt32();
				section.Name = binaryReader.ReadBytes(128);
				if (section.Name.Length > 1 && section.Name[1] == base.Keys[1])
				{
					base.isEncrypted = true;
					section.Name = TextEncrypt.BikeyXor(section.Name, base.Keys);
				}
				sectionCollection.Add(section);
				for (int j = 0; j < section.XStringCount; j++)
				{
					XString xString = new XString();
					xString.ResourceIndex = binaryReader.ReadInt32();
					if (version == 65281)
					{
						xString.ParameterOrder.Add(binaryReader.ReadInt32());
						int num2 = binaryReader.ReadInt32();
						byte[] array = binaryReader.ReadBytes(2 * num2);
						if (base.isEncrypted)
						{
							array = TextEncrypt.BikeyXor(array, base.Keys);
						}
						xString.TextString.Add(array);
						xString.TextStringLength.Add(num2);
						sectionCollection.Sections[i].XStrings.Add(xString.ResourceIndex, xString.ParameterOrder[0], xString.TextStringLength[0], xString.TextString[0]);
						continue;
					}
					for (int k = 0; k < base.MaxLanguages; k++)
					{
						xString.ParameterOrder.Add(binaryReader.ReadInt32());
					}
					for (int l = 0; l < base.MaxLanguages; l++)
					{
						int num2 = binaryReader.ReadInt32();
						byte[] array = binaryReader.ReadBytes(2 * num2);
						if (base.isEncrypted)
						{
							array = TextEncrypt.BikeyXor(array, base.Keys);
						}
						xString.TextString.Add(array);
						xString.TextStringLength.Add(num2);
					}
					sectionCollection.Sections[i].XStrings.Add(xString.ResourceIndex, xString.ParameterOrder, xString.TextStringLength, xString.TextString);
				}
			}
		}
		OnLoaded(EventArgs.Empty);
	}

	public override void write(string filename, bool withEncryption = false)
	{
		using (BinaryWriter binaryWriter = new BinaryWriter(File.Open(filename, FileMode.Create)))
		{
			binaryWriter.Write(header);
			binaryWriter.Write(version);
			binaryWriter.Write(base.totalSectionCount);
			foreach (Section section in sectionCollection.Sections)
			{
				byte[] buffer = ((base.isEncrypted || withEncryption) ? TextEncrypt.BikeyXor((byte[])section.Name.Clone(), base.Keys) : section.Name);
				binaryWriter.Write(section.XStringCount);
				binaryWriter.Write(buffer);
				if (version == 65281)
				{
					foreach (XString row in section.XStrings.Rows)
					{
						if (row.TextString.Count < 1)
						{
							row.TextString.Add(new byte[0]);
						}
						byte[] array = ((base.isEncrypted || withEncryption) ? TextEncrypt.BikeyXor(row.TextString[0], base.Keys) : row.TextString[0]);
						if (row.ParameterOrder.Count < 1)
						{
							row.ParameterOrder.Add(0);
						}
						if (row.TextStringLength.Count < 1)
						{
							row.TextStringLength.Add(Encoding.Unicode.GetString(array).Length);
						}
						binaryWriter.Write(row.ResourceIndex);
						binaryWriter.Write(row.ParameterOrder[0]);
						binaryWriter.Write(row.TextStringLength[0]);
						binaryWriter.Write(array);
					}
					continue;
				}
				foreach (XString row2 in section.XStrings.Rows)
				{
					binaryWriter.Write(row2.ResourceIndex);
					if (row2.ParameterOrder.Count <= base.MaxLanguages)
					{
						for (int i = row2.ParameterOrder.Count; i < base.MaxLanguages; i++)
						{
							row2.ParameterOrder.Add(0);
						}
					}
					if (row2.TextString.Count <= base.MaxLanguages)
					{
						for (int j = row2.TextString.Count; j < base.MaxLanguages; j++)
						{
							row2.TextString[j] = new byte[0];
						}
					}
					if (row2.TextStringLength.Count <= base.MaxLanguages)
					{
						for (int k = row2.TextStringLength.Count; k < base.MaxLanguages; k++)
						{
							row2.TextStringLength[k] = Encoding.Unicode.GetString(row2.TextString[k]).Length;
						}
					}
					foreach (int item in row2.ParameterOrder)
					{
						binaryWriter.Write(item);
					}
					for (int l = 0; l < base.MaxLanguages; l++)
					{
						byte[] buffer2 = ((base.isEncrypted || withEncryption) ? TextEncrypt.BikeyXor(row2.TextString[l], base.Keys) : row2.TextString[l]);
						binaryWriter.Write(row2.TextStringLength[l]);
						binaryWriter.Write(buffer2);
					}
				}
			}
		}
		if (withEncryption)
		{
			base.isEncrypted = true;
		}
		base.Filename = filename;
	}
}
