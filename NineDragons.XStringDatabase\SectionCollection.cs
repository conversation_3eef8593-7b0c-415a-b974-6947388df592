using System.ComponentModel;

namespace NineDragons.XStringDatabase;

public class SectionCollection : INotifyPropertyChanged
{
	private BindingList<Section> listSection = new BindingList<Section>();

	public BindingList<Section> Sections
	{
		get
		{
			return listSection;
		}
		set
		{
			listSection = value;
		}
	}

	public event PropertyChangedEventHandler PropertyChanged;

	public SectionCollection()
	{
		BindEvents();
	}

	private void BindEvents()
	{
		listSection.ListChanged += SectionListChangedEventHandler;
	}

	public void Add(int count, byte[] name)
	{
		Section item = new Section
		{
			XStringCount = count,
			Name = name
		};
		listSection.Add(item);
	}

	public void Add(Section section)
	{
		Add(section.XStringCount, section.Name);
	}

	private void SectionListChangedEventHandler(object sender, ListChangedEventArgs e)
	{
		NotifyPropertyChanged("Sections");
	}

	private void NotifyPropertyChanged(string name)
	{
		if (this.PropertyChanged != null)
		{
			this.PropertyChanged(this, new PropertyChangedEventArgs(name));
		}
	}
}
