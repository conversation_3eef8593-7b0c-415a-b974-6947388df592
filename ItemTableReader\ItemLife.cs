using System.IO;

namespace ItemTableReader;

internal class ItemLife : ItemBase
{
	private static uint XSD_START_INDEX = 55000u;

	public uint _xsdName;

	public uint _xsdInfo;

	private static Map<ItemLife> mapLifes = new Map<ItemLife>();

	public uint xsdName;

	public uint xsdInfo;

	public static uint Size => 66u;

	public override ushort ItemRank
	{
		get
		{
			return base.ItemRank;
		}
		set
		{
			base.ItemRank = value;
			unknownBytes[31] = (byte)value;
			unknownBytes[32] = (byte)(value >> 8);
		}
	}

	public static Map<ItemLife> Lifes => mapLifes;

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		_xsdName = binaryReader.ReadUInt32();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		_xsdInfo = binaryReader.ReadUInt32();
		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		base.Name = XsdManager.GetInfoFrom("LifeName", xsdName);
		base.Description = XsdManager.GetInfoFrom("LifeInfo", xsdInfo);
		unknownBytes.AddRange(binaryReader.ReadBytes(52));
		base.ItemRank = (ushort)(unknownBytes[31] + unknownBytes[32] * 256);
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(_xsdName);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(unknownBytes.ToArray(), num, 52);
		num += 52;
	}
}
