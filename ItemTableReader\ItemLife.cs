using System.IO;

namespace ItemTableReader;

internal class ItemLife : ItemBase
{
	private static uint XSD_START_INDEX = 55000u;

	public uint _xsdName;

	public uint _xsdInfo;

	private static Map<ItemLife> mapLifes = new Map<ItemLife>();

	public uint xsdName;

	public uint xsdInfo;

	// LIFEPROPERTY structure fields
	public sbyte objType3;
	public sbyte clanID;
	public ushort durability;
	public byte count;
	public sbyte balance;
	public sbyte prob;
	public sbyte luck;
	public sbyte strength;
	public sbyte applyClan;
	public int iUnk1; // clanpoint1
	public int iUnk2; // clanpoint2
	public uint price;
	public sbyte rank;
	public ushort abilityID;
	public byte maxMerge;
	public sbyte repairProb;
	public short modelIndex;
	public short iconNo;
	public ushort itemRank;
	public byte grade;
	public byte quality;
	public byte quality2;
	public sbyte dump;
	public sbyte userTrade;
	public sbyte npcTrade;
	public byte fame;
	public sbyte cashCheck;
	public sbyte cUnk1;
	public short sUnk1;
	public short sUnk2;
	public sbyte cUnk2;
	public short sUnk3;
	public short sUnk4;
	public short sUnk5;
	public sbyte cUnk3;

	public static uint Size => 66u;

	public override ushort ItemRank
	{
		get
		{
			return itemRank;
		}
		set
		{
			itemRank = value;
		}
	}

	public override short IconIndex
	{
		get
		{
			return iconNo;
		}
		set
		{
			iconNo = value;
		}
	}

	public override short ModelIndex
	{
		get
		{
			return modelIndex;
		}
		set
		{
			modelIndex = value;
		}
	}

	public static Map<ItemLife> Lifes => mapLifes;

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		_xsdName = binaryReader.ReadUInt32();
		objType3 = binaryReader.ReadSByte();
		clanID = binaryReader.ReadSByte();
		_xsdInfo = binaryReader.ReadUInt32();
		durability = binaryReader.ReadUInt16();
		count = binaryReader.ReadByte();
		balance = binaryReader.ReadSByte();
		prob = binaryReader.ReadSByte();
		luck = binaryReader.ReadSByte();
		strength = binaryReader.ReadSByte();
		applyClan = binaryReader.ReadSByte();
		iUnk1 = binaryReader.ReadInt32();
		iUnk2 = binaryReader.ReadInt32();
		price = binaryReader.ReadUInt32();
		rank = binaryReader.ReadSByte();
		abilityID = binaryReader.ReadUInt16();
		maxMerge = binaryReader.ReadByte();
		repairProb = binaryReader.ReadSByte();
		modelIndex = binaryReader.ReadInt16();
		iconNo = binaryReader.ReadInt16();
		itemRank = binaryReader.ReadUInt16();
		grade = binaryReader.ReadByte();
		quality = binaryReader.ReadByte();
		quality2 = binaryReader.ReadByte();
		dump = binaryReader.ReadSByte();
		userTrade = binaryReader.ReadSByte();
		npcTrade = binaryReader.ReadSByte();
		fame = binaryReader.ReadByte();
		cashCheck = binaryReader.ReadSByte();
		cUnk1 = binaryReader.ReadSByte();
		sUnk1 = binaryReader.ReadInt16();
		sUnk2 = binaryReader.ReadInt16();
		cUnk2 = binaryReader.ReadSByte();
		sUnk3 = binaryReader.ReadInt16();
		sUnk4 = binaryReader.ReadInt16();
		sUnk5 = binaryReader.ReadInt16();
		cUnk3 = binaryReader.ReadSByte();

		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		base.Name = XsdManager.GetInfoFrom("LifeName", xsdName);
		base.Description = XsdManager.GetInfoFrom("LifeInfo", xsdInfo);
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(objType3);
		binaryWriter.Write(clanID);
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(durability);
		binaryWriter.Write(count);
		binaryWriter.Write(balance);
		binaryWriter.Write(prob);
		binaryWriter.Write(luck);
		binaryWriter.Write(strength);
		binaryWriter.Write(applyClan);
		binaryWriter.Write(iUnk1);
		binaryWriter.Write(iUnk2);
		binaryWriter.Write(price);
		binaryWriter.Write(rank);
		binaryWriter.Write(abilityID);
		binaryWriter.Write(maxMerge);
		binaryWriter.Write(repairProb);
		binaryWriter.Write(modelIndex);
		binaryWriter.Write(iconNo);
		binaryWriter.Write(itemRank);
		binaryWriter.Write(grade);
		binaryWriter.Write(quality);
		binaryWriter.Write(quality2);
		binaryWriter.Write(dump);
		binaryWriter.Write(userTrade);
		binaryWriter.Write(npcTrade);
		binaryWriter.Write(fame);
		binaryWriter.Write(cashCheck);
		binaryWriter.Write(cUnk1);
		binaryWriter.Write(sUnk1);
		binaryWriter.Write(sUnk2);
		binaryWriter.Write(cUnk2);
		binaryWriter.Write(sUnk3);
		binaryWriter.Write(sUnk4);
		binaryWriter.Write(sUnk5);
		binaryWriter.Write(cUnk3);
	}
}
