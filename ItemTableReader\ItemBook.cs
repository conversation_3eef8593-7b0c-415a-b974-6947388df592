using System.IO;

namespace ItemTableReader;

internal class ItemBook : ItemBase
{
	private static uint XSD_START_INDEX = 20000u;

	private uint _xsdName;

	private uint _xsdInfo;

	public uint xsdName;

	public uint xsdInfo;

	public static Map<ItemBook> mapBooks = new Map<ItemBook>();

	public sbyte thirdType;

	public ushort skillID;

	public byte value;

	public byte skillGroup;

	public byte skillClass;

	public sbyte skillStep;

	public byte skillLevel;

	public ushort abilityID;

	public ushort questID;

	public ushort hiddenID;

	public sbyte prob;

	public ushort delay;

	public bool delete;

	public uint applyTime;

	public ushort cooldown;

	public byte clan;

	public byte characGrade;

	public byte classID;

	public RequiredSkill[] skills = new RequiredSkill[3];

	public ushort requiredAbilityID;

	public sbyte requiredAbilityStep;

	public ushort requiredLevel;

	public ushort requiredStr;

	public ushort requiredEss;

	public ushort requiredWis;

	public ushort requiredCon;

	public ushort requiredDex;

	public ushort requiredHP;

	public ushort requiredVE;

	public static uint Size => 109u;

	public static Map<ItemBook> Books => mapBooks;

	public sbyte ThirdType => thirdType;

	public ushort SkillID => skillID;

	public byte Value => value;

	public byte SkillGroup => skillGroup;

	public byte SkillClass => skillClass;

	public sbyte SkillStep => skillStep;

	public byte SkillLevel => skillLevel;

	public ushort AbilityID => abilityID;

	public ushort QuestID => questID;

	public byte Clan => clan;

	public byte CharacterGrade => characGrade;

	public byte Class => classID;

	public void updateXsdName()
	{
		_xsdName = xsdName + XSD_START_INDEX;
	}

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		_xsdName = binaryReader.ReadUInt32();
		thirdType = binaryReader.ReadSByte();
		_xsdInfo = binaryReader.ReadUInt32();
		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		int key = (int)xsdName;
		if (XsdManager.Maps["BookName"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["BookName"], XsdManager.TableNames["BookName"]);
		}
		if (XsdManager.Maps["BookInfo"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["BookInfo"], XsdManager.TableNames["BookInfo"]);
		}
		if (XsdManager.Maps["BookName"].ContainsKey(key))
		{
			base.Name = XsdManager.Maps["BookName"][key];
		}
		key = (int)xsdInfo;
		if (XsdManager.Maps["BookInfo"].ContainsKey(key))
		{
			base.Description = XsdManager.Maps["BookInfo"][key];
		}
		skillID = binaryReader.ReadUInt16();
		value = binaryReader.ReadByte();
		skillGroup = binaryReader.ReadByte();
		skillClass = binaryReader.ReadByte();
		skillStep = binaryReader.ReadSByte();
		skillLevel = binaryReader.ReadByte();
		abilityID = binaryReader.ReadUInt16();
		questID = binaryReader.ReadUInt16();
		clan = binaryReader.ReadByte();
		characGrade = binaryReader.ReadByte();
		classID = binaryReader.ReadByte();
		for (int i = 0; i < 3; i++)
		{
			skills[i] = new RequiredSkill();
			skills[i].Load(new MemoryStream(binaryReader.ReadBytes(3)));
		}
		requiredAbilityID = binaryReader.ReadUInt16();
		requiredAbilityStep = binaryReader.ReadSByte();
		hiddenID = binaryReader.ReadUInt16();
		prob = binaryReader.ReadSByte();
		delay = binaryReader.ReadUInt16();
		delete = binaryReader.ReadBoolean();
		applyTime = binaryReader.ReadUInt32();
		cooldown = binaryReader.ReadUInt16();
		base.ApplyClan = binaryReader.ReadSByte();
		base.ClanPoint1 = binaryReader.ReadInt32();
		base.ClanPoint2 = binaryReader.ReadInt32();
		base.Price = binaryReader.ReadUInt32();
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		base.Grade = binaryReader.ReadByte();
		ItemRank = binaryReader.ReadUInt16();
		base.Quality = binaryReader.ReadByte();
		base.Quality2 = binaryReader.ReadByte();
		requiredLevel = binaryReader.ReadUInt16();
		requiredStr = binaryReader.ReadUInt16();
		requiredEss = binaryReader.ReadUInt16();
		requiredWis = binaryReader.ReadUInt16();
		requiredCon = binaryReader.ReadUInt16();
		requiredDex = binaryReader.ReadUInt16();
		requiredHP = binaryReader.ReadUInt16();
		requiredVE = binaryReader.ReadUInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		base.BlockDrop = binaryReader.ReadBoolean();
		BlockTrade = binaryReader.ReadByte();
		BlockNpcSell = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(7));
		base.CashCheck = binaryReader.ReadSByte();
		base.Time = binaryReader.ReadInt16();
		base.BlockStorage = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(3));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(thirdType);
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(skillID);
		binaryWriter.Write(value);
		binaryWriter.Write(skillGroup);
		binaryWriter.Write(skillClass);
		binaryWriter.Write(skillStep);
		binaryWriter.Write(skillLevel);
		binaryWriter.Write(abilityID);
		binaryWriter.Write(questID);
		binaryWriter.Write(clan);
		binaryWriter.Write(characGrade);
		binaryWriter.Write(classID);
		for (int i = 0; i < 3; i++)
		{
			skills[i].Save(s);
		}
		binaryWriter.Write(requiredAbilityID);
		binaryWriter.Write(requiredAbilityStep);
		binaryWriter.Write(hiddenID);
		binaryWriter.Write(prob);
		binaryWriter.Write(delay);
		binaryWriter.Write(delete);
		binaryWriter.Write(applyTime);
		binaryWriter.Write(cooldown);
		binaryWriter.Write(base.ApplyClan);
		binaryWriter.Write(base.ClanPoint1);
		binaryWriter.Write(base.ClanPoint2);
		binaryWriter.Write(base.Price);
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(ItemRank);
		binaryWriter.Write(base.Quality);
		binaryWriter.Write(base.Quality2);
		binaryWriter.Write(requiredLevel);
		binaryWriter.Write(requiredStr);
		binaryWriter.Write(requiredEss);
		binaryWriter.Write(requiredWis);
		binaryWriter.Write(requiredCon);
		binaryWriter.Write(requiredDex);
		binaryWriter.Write(requiredHP);
		binaryWriter.Write(requiredVE);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(base.BlockDrop);
		binaryWriter.Write(BlockTrade);
		binaryWriter.Write(BlockNpcSell);
		binaryWriter.Write(unknownBytes.ToArray(), num, 7);
		num += 7;
		binaryWriter.Write(base.CashCheck);
		binaryWriter.Write(base.Time);
		binaryWriter.Write(base.BlockStorage);
		binaryWriter.Write(unknownBytes.ToArray(), num, 3);
		num += 3;
	}
}
