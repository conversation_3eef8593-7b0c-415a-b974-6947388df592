using System;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormBooks : Form
{
	private static ItemBook tempBook = new ItemBook();

	private IContainer components;

	private ListBox listBoxBooks;

	private ByteViewer byteViewer = new ByteViewer();

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPageUnknown;

	private TableLayoutPanel tableLayoutPanel1;

	private Label label1;

	private Label label2;

	private TextBox textBoxSkillID;

	private TextBox textBoxValue;

	private Label label3;

	private Label label4;

	private Label label5;

	private Label label6;

	private Label label7;

	private Label label8;

	private TextBox textBoxSkillGroup;

	private TextBox textBoxSkillClass;

	private TextBox textBoxSkillStep;

	private TextBox textBoxSkillLevel;

	private TextBox textBoxAbilityID;

	private TextBox textBoxQuestID;

	private Label label9;

	private TextBox textBoxClan;

	private Label label10;

	private TextBox textBoxCharacGrade;

	private Label labelClass;

	private TextBox textBoxClass;

	private DataGridViewCheckBoxColumn canDropDataGridViewCheckBoxColumn;

	private DataGridViewCheckBoxColumn canStoreDataGridViewCheckBoxColumn;

	private DataGridViewTextBoxColumn contrib1DataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn contrib2DataGridViewTextBoxColumn;

	private BindingSource itemBookBindingSource;

	private Label label11;

	private TextBox textBoxPrice;

	private Label label12;

	private ComboBox unknownByteIndex;

	private TextBox unknownByteValue;

	private Button btn_copyItem;

	private Button btn_pasteItem;

	private Label label13;

	private TextBox textBoxItemRank;

	private Label label14;

	private TextBox textBoxXSDIndex;

	public FormBooks()
	{
		InitializeComponent();
		for (int i = 0; i < 12; i++)
		{
			unknownByteIndex.Items.Add(i.ToString());
		}
	}

	private void FormBooks_Load(object sender, EventArgs e)
	{
		listBoxBooks.BeginUpdate();
		foreach (ItemBook value in ItemBook.Books.Values)
		{
			listBoxBooks.Items.Add(value);
		}
		listBoxBooks.EndUpdate();
		listBoxBooks.DisplayMember = "FullName";
		listBoxBooks.ValueMember = "ID";
	}

	private void listBoxBooks_SelectedIndexChanged(object sender, EventArgs e)
	{
		ItemBook itemBook = listBoxBooks.SelectedItem as ItemBook;
		tabPageUnknown.Controls.Add(byteViewer);
		byteViewer.Dock = DockStyle.Fill;
		byteViewer.SetBytes(itemBook.unknownBytes.ToArray());
		textBoxXSDIndex.Text = itemBook.xsdName.ToString();
		textBoxItemRank.Text = itemBook.ItemRank.ToString();
		textBoxSkillID.Text = itemBook.SkillID.ToString();
		textBoxSkillLevel.Text = itemBook.SkillLevel.ToString();
		textBoxSkillClass.Text = itemBook.SkillClass.ToString();
		textBoxSkillGroup.Text = itemBook.SkillGroup.ToString();
		textBoxSkillStep.Text = itemBook.SkillStep.ToString();
		textBoxValue.Text = itemBook.Value.ToString();
		textBoxAbilityID.Text = itemBook.AbilityID.ToString();
		textBoxQuestID.Text = itemBook.QuestID.ToString();
		textBoxClan.Text = itemBook.Clan.ToString();
		textBoxClass.Text = itemBook.Class.ToString();
		textBoxCharacGrade.Text = itemBook.CharacterGrade.ToString();
		textBoxPrice.Text = itemBook.Price.ToString();
		if (unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemBook.unknownBytes.Count)
		{
			unknownByteValue.Text = itemBook.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
		}
	}

	private void textBoxSkillID_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).skillID = Convert.ToUInt16(textBoxSkillID.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).value = Convert.ToByte(textBoxValue.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxSkillGroup_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).skillGroup = Convert.ToByte(textBoxSkillGroup.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxSkillClass_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).skillClass = Convert.ToByte(textBoxSkillClass.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxSkillStep_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).skillStep = Convert.ToSByte(textBoxSkillStep.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxSkillLevel_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).skillLevel = Convert.ToByte(textBoxSkillLevel.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxAbilityID_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).abilityID = Convert.ToUInt16(textBoxAbilityID.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxQuestID_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).questID = Convert.ToUInt16(textBoxQuestID.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxClan_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).clan = Convert.ToByte(textBoxClan.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxCharacGrade_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).characGrade = Convert.ToByte(textBoxCharacGrade.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxClass_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxBooks.SelectedItem as ItemBook).classID = Convert.ToByte(textBoxClass.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxPrice_TextChanged(object sender, EventArgs e)
	{
		if (listBoxBooks.SelectedItem is ItemBook itemBook && uint.TryParse(textBoxPrice.Text, out var result))
		{
			itemBook.Price = result;
		}
	}

	private void unknownByteIndex_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxBooks.SelectedItem is ItemBook itemBook && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemBook.unknownBytes.Count)
			{
				unknownByteValue.Text = itemBook.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxBooks.SelectedItem is ItemBook itemBook && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemBook.unknownBytes.Count)
			{
				itemBook.unknownBytes[unknownByteIndex.SelectedIndex] = Convert.ToByte(unknownByteValue.Text);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_copyItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (!(listBoxBooks.SelectedItem is ItemBook itemBook))
			{
				return;
			}
			tempBook.Type = itemBook.Type;
			tempBook.SecondType = itemBook.SecondType;
			tempBook.ModelIndex = itemBook.ModelIndex;
			tempBook.thirdType = itemBook.thirdType;
			tempBook.ApplyClan = itemBook.ApplyClan;
			tempBook.characGrade = itemBook.characGrade;
			tempBook.IconIndex = itemBook.IconIndex;
			tempBook.skillID = itemBook.skillID;
			tempBook.ClanPoint1 = itemBook.ClanPoint1;
			tempBook.ClanPoint2 = itemBook.ClanPoint2;
			tempBook.value = itemBook.value;
			tempBook.skillGroup = itemBook.skillGroup;
			tempBook.skillClass = itemBook.skillClass;
			tempBook.skillStep = itemBook.skillStep;
			tempBook.skillLevel = itemBook.skillLevel;
			tempBook.Grade = itemBook.Grade;
			tempBook.Price = itemBook.Price;
			tempBook.ItemRank = itemBook.ItemRank;
			tempBook.abilityID = itemBook.abilityID;
			tempBook.questID = itemBook.questID;
			tempBook.classID = itemBook.classID;
			tempBook.requiredAbilityID = itemBook.requiredAbilityID;
			tempBook.requiredAbilityStep = itemBook.requiredAbilityStep;
			tempBook.clan = itemBook.clan;
			tempBook.BlockDrop = itemBook.BlockDrop;
			tempBook.hiddenID = itemBook.hiddenID;
			tempBook.BlockNpcSell = itemBook.BlockNpcSell;
			tempBook.xsdName = itemBook.xsdName;
			tempBook.xsdInfo = itemBook.xsdInfo;
			tempBook.prob = itemBook.prob;
			tempBook.delay = itemBook.delay;
			tempBook.delete = itemBook.delete;
			tempBook.applyTime = itemBook.applyTime;
			tempBook.cooldown = itemBook.cooldown;
			tempBook.Quality = itemBook.Quality;
			tempBook.Quality2 = itemBook.Quality2;
			tempBook.requiredLevel = itemBook.requiredLevel;
			tempBook.requiredStr = itemBook.requiredStr;
			tempBook.requiredEss = itemBook.requiredEss;
			tempBook.requiredWis = itemBook.requiredWis;
			tempBook.requiredCon = itemBook.requiredCon;
			tempBook.requiredDex = itemBook.requiredDex;
			tempBook.requiredHP = itemBook.requiredHP;
			tempBook.requiredVE = itemBook.requiredVE;
			tempBook.BlockDrop = itemBook.BlockDrop;
			tempBook.BlockTrade = itemBook.BlockTrade;
			tempBook.BlockNpcSell = itemBook.BlockNpcSell;
			tempBook.CashCheck = itemBook.CashCheck;
			tempBook.Time = itemBook.Time;
			tempBook.BlockStorage = itemBook.BlockStorage;
			for (int i = 0; i < 3; i++)
			{
				tempBook.skills[i] = itemBook.skills[i];
			}
			if (tempBook.unknownBytes.Count == 0)
			{
				byte item = 0;
				for (int j = 0; j < 12; j++)
				{
					tempBook.unknownBytes.Add(item);
				}
			}
			for (int k = 0; k < itemBook.unknownBytes.Count; k++)
			{
				tempBook.unknownBytes[k] = itemBook.unknownBytes[k];
			}
			btn_pasteItem.Enabled = true;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_pasteItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxBooks.SelectedItem is ItemBook itemBook)
			{
				itemBook.Type = tempBook.Type;
				itemBook.SecondType = tempBook.SecondType;
				itemBook.ModelIndex = tempBook.ModelIndex;
				itemBook.thirdType = tempBook.thirdType;
				itemBook.ApplyClan = tempBook.ApplyClan;
				itemBook.characGrade = tempBook.characGrade;
				itemBook.IconIndex = tempBook.IconIndex;
				itemBook.skillID = tempBook.skillID;
				itemBook.ClanPoint1 = tempBook.ClanPoint1;
				itemBook.ClanPoint2 = tempBook.ClanPoint2;
				itemBook.value = tempBook.value;
				itemBook.skillGroup = tempBook.skillGroup;
				itemBook.skillClass = tempBook.skillClass;
				itemBook.skillStep = tempBook.skillStep;
				itemBook.skillLevel = tempBook.skillLevel;
				itemBook.Grade = tempBook.Grade;
				itemBook.Price = tempBook.Price;
				itemBook.ItemRank = tempBook.ItemRank;
				itemBook.abilityID = tempBook.abilityID;
				itemBook.questID = tempBook.questID;
				itemBook.classID = tempBook.classID;
				itemBook.requiredAbilityID = tempBook.requiredAbilityID;
				itemBook.requiredAbilityStep = tempBook.requiredAbilityStep;
				itemBook.clan = tempBook.clan;
				itemBook.BlockDrop = tempBook.BlockDrop;
				itemBook.hiddenID = tempBook.hiddenID;
				itemBook.BlockNpcSell = tempBook.BlockNpcSell;
				itemBook.xsdName = tempBook.xsdName;
				itemBook.xsdInfo = tempBook.xsdInfo;
				itemBook.prob = tempBook.prob;
				itemBook.delay = tempBook.delay;
				itemBook.delete = tempBook.delete;
				itemBook.applyTime = tempBook.applyTime;
				itemBook.cooldown = tempBook.cooldown;
				itemBook.Quality = tempBook.Quality;
				itemBook.Quality2 = tempBook.Quality2;
				itemBook.requiredLevel = tempBook.requiredLevel;
				itemBook.requiredStr = tempBook.requiredStr;
				itemBook.requiredEss = tempBook.requiredEss;
				itemBook.requiredWis = tempBook.requiredWis;
				itemBook.requiredCon = tempBook.requiredCon;
				itemBook.requiredDex = tempBook.requiredDex;
				itemBook.requiredHP = tempBook.requiredHP;
				itemBook.requiredVE = tempBook.requiredVE;
				itemBook.BlockDrop = tempBook.BlockDrop;
				itemBook.BlockTrade = tempBook.BlockTrade;
				itemBook.BlockNpcSell = tempBook.BlockNpcSell;
				itemBook.CashCheck = tempBook.CashCheck;
				itemBook.Time = tempBook.Time;
				itemBook.BlockStorage = tempBook.BlockStorage;
				for (int i = 0; i < 3; i++)
				{
					itemBook.skills[i].SkillID = tempBook.skills[i].SkillID;
					itemBook.skills[i].SkillStep = tempBook.skills[i].SkillStep;
				}
				for (int j = 0; j < tempBook.unknownBytes.Count; j++)
				{
					itemBook.unknownBytes[j] = tempBook.unknownBytes[j];
				}
			}
			listBoxBooks_SelectedIndexChanged(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxItemRank_TextChanged(object sender, EventArgs e)
	{
		if (listBoxBooks.SelectedItem is ItemBook itemBook && ushort.TryParse(textBoxItemRank.Text, out var result))
		{
			itemBook.ItemRank = result;
		}
	}

	private void textBoxXSDIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemBook obj = listBoxBooks.SelectedItem as ItemBook;
			obj.xsdName = Convert.ToUInt16(textBoxXSDIndex.Text);
			obj.updateXsdName();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.listBoxBooks = new System.Windows.Forms.ListBox();
		this.tabControl1 = new System.Windows.Forms.TabControl();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
		this.label12 = new System.Windows.Forms.Label();
		this.label11 = new System.Windows.Forms.Label();
		this.label1 = new System.Windows.Forms.Label();
		this.label2 = new System.Windows.Forms.Label();
		this.textBoxSkillID = new System.Windows.Forms.TextBox();
		this.textBoxValue = new System.Windows.Forms.TextBox();
		this.label3 = new System.Windows.Forms.Label();
		this.label4 = new System.Windows.Forms.Label();
		this.label5 = new System.Windows.Forms.Label();
		this.label6 = new System.Windows.Forms.Label();
		this.label7 = new System.Windows.Forms.Label();
		this.label8 = new System.Windows.Forms.Label();
		this.textBoxSkillGroup = new System.Windows.Forms.TextBox();
		this.textBoxSkillClass = new System.Windows.Forms.TextBox();
		this.textBoxSkillStep = new System.Windows.Forms.TextBox();
		this.textBoxSkillLevel = new System.Windows.Forms.TextBox();
		this.textBoxAbilityID = new System.Windows.Forms.TextBox();
		this.textBoxQuestID = new System.Windows.Forms.TextBox();
		this.label9 = new System.Windows.Forms.Label();
		this.textBoxClan = new System.Windows.Forms.TextBox();
		this.label10 = new System.Windows.Forms.Label();
		this.textBoxCharacGrade = new System.Windows.Forms.TextBox();
		this.labelClass = new System.Windows.Forms.Label();
		this.textBoxClass = new System.Windows.Forms.TextBox();
		this.textBoxPrice = new System.Windows.Forms.TextBox();
		this.unknownByteIndex = new System.Windows.Forms.ComboBox();
		this.unknownByteValue = new System.Windows.Forms.TextBox();
		this.btn_pasteItem = new System.Windows.Forms.Button();
		this.btn_copyItem = new System.Windows.Forms.Button();
		this.label13 = new System.Windows.Forms.Label();
		this.textBoxItemRank = new System.Windows.Forms.TextBox();
		this.tabPageUnknown = new System.Windows.Forms.TabPage();
		this.itemBookBindingSource = new System.Windows.Forms.BindingSource(this.components);
		this.label14 = new System.Windows.Forms.Label();
		this.textBoxXSDIndex = new System.Windows.Forms.TextBox();
		this.tabControl1.SuspendLayout();
		this.tabPage1.SuspendLayout();
		this.tableLayoutPanel1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.itemBookBindingSource).BeginInit();
		base.SuspendLayout();
		this.listBoxBooks.Dock = System.Windows.Forms.DockStyle.Left;
		this.listBoxBooks.FormattingEnabled = true;
		this.listBoxBooks.Location = new System.Drawing.Point(0, 0);
		this.listBoxBooks.Name = "listBoxBooks";
		this.listBoxBooks.Size = new System.Drawing.Size(174, 527);
		this.listBoxBooks.TabIndex = 0;
		this.listBoxBooks.SelectedIndexChanged += new System.EventHandler(listBoxBooks_SelectedIndexChanged);
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPageUnknown);
		this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tabControl1.Location = new System.Drawing.Point(174, 0);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new System.Drawing.Size(376, 527);
		this.tabControl1.TabIndex = 0;
		this.tabPage1.Controls.Add(this.tableLayoutPanel1);
		this.tabPage1.Location = new System.Drawing.Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(368, 501);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "Details";
		this.tabPage1.UseVisualStyleBackColor = true;
		this.tableLayoutPanel1.ColumnCount = 3;
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150f));
		this.tableLayoutPanel1.Controls.Add(this.label12, 0, 13);
		this.tableLayoutPanel1.Controls.Add(this.label11, 0, 12);
		this.tableLayoutPanel1.Controls.Add(this.label1, 0, 0);
		this.tableLayoutPanel1.Controls.Add(this.label2, 0, 1);
		this.tableLayoutPanel1.Controls.Add(this.textBoxSkillID, 1, 0);
		this.tableLayoutPanel1.Controls.Add(this.textBoxValue, 1, 1);
		this.tableLayoutPanel1.Controls.Add(this.label3, 0, 2);
		this.tableLayoutPanel1.Controls.Add(this.label4, 0, 3);
		this.tableLayoutPanel1.Controls.Add(this.label5, 0, 4);
		this.tableLayoutPanel1.Controls.Add(this.label6, 0, 5);
		this.tableLayoutPanel1.Controls.Add(this.label7, 0, 6);
		this.tableLayoutPanel1.Controls.Add(this.label8, 0, 7);
		this.tableLayoutPanel1.Controls.Add(this.textBoxSkillGroup, 1, 2);
		this.tableLayoutPanel1.Controls.Add(this.textBoxSkillClass, 1, 3);
		this.tableLayoutPanel1.Controls.Add(this.textBoxSkillStep, 1, 4);
		this.tableLayoutPanel1.Controls.Add(this.textBoxSkillLevel, 1, 5);
		this.tableLayoutPanel1.Controls.Add(this.textBoxAbilityID, 1, 6);
		this.tableLayoutPanel1.Controls.Add(this.textBoxQuestID, 1, 7);
		this.tableLayoutPanel1.Controls.Add(this.label9, 0, 9);
		this.tableLayoutPanel1.Controls.Add(this.textBoxClan, 1, 9);
		this.tableLayoutPanel1.Controls.Add(this.label10, 0, 10);
		this.tableLayoutPanel1.Controls.Add(this.textBoxCharacGrade, 1, 10);
		this.tableLayoutPanel1.Controls.Add(this.labelClass, 0, 11);
		this.tableLayoutPanel1.Controls.Add(this.textBoxClass, 1, 11);
		this.tableLayoutPanel1.Controls.Add(this.textBoxPrice, 1, 12);
		this.tableLayoutPanel1.Controls.Add(this.unknownByteIndex, 1, 13);
		this.tableLayoutPanel1.Controls.Add(this.unknownByteValue, 2, 13);
		this.tableLayoutPanel1.Controls.Add(this.label13, 0, 14);
		this.tableLayoutPanel1.Controls.Add(this.textBoxItemRank, 1, 14);
		this.tableLayoutPanel1.Controls.Add(this.btn_copyItem, 1, 17);
		this.tableLayoutPanel1.Controls.Add(this.btn_pasteItem, 1, 16);
		this.tableLayoutPanel1.Controls.Add(this.label14, 0, 15);
		this.tableLayoutPanel1.Controls.Add(this.textBoxXSDIndex, 1, 15);
		this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel1.Name = "tableLayoutPanel1";
		this.tableLayoutPanel1.RowCount = 19;
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 31f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 34f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 31f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8f));
		this.tableLayoutPanel1.Size = new System.Drawing.Size(362, 495);
		this.tableLayoutPanel1.TabIndex = 0;
		this.label12.AutoSize = true;
		this.label12.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label12.Location = new System.Drawing.Point(3, 312);
		this.label12.Name = "label12";
		this.label12.Size = new System.Drawing.Size(79, 27);
		this.label12.TabIndex = 25;
		this.label12.Text = "UnknownBytes";
		this.label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label11.AutoSize = true;
		this.label11.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label11.Location = new System.Drawing.Point(3, 286);
		this.label11.Name = "label11";
		this.label11.Size = new System.Drawing.Size(79, 26);
		this.label11.TabIndex = 24;
		this.label11.Text = "price";
		this.label11.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label1.AutoSize = true;
		this.label1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label1.Location = new System.Drawing.Point(3, 0);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(79, 26);
		this.label1.TabIndex = 0;
		this.label1.Text = "skillID";
		this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label2.AutoSize = true;
		this.label2.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label2.Location = new System.Drawing.Point(3, 26);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(79, 26);
		this.label2.TabIndex = 1;
		this.label2.Text = "value";
		this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxSkillID.Location = new System.Drawing.Point(88, 3);
		this.textBoxSkillID.Name = "textBoxSkillID";
		this.textBoxSkillID.Size = new System.Drawing.Size(100, 20);
		this.textBoxSkillID.TabIndex = 2;
		this.textBoxSkillID.TextChanged += new System.EventHandler(textBoxSkillID_TextChanged);
		this.textBoxValue.Location = new System.Drawing.Point(88, 29);
		this.textBoxValue.Name = "textBoxValue";
		this.textBoxValue.Size = new System.Drawing.Size(100, 20);
		this.textBoxValue.TabIndex = 3;
		this.textBoxValue.TextChanged += new System.EventHandler(textBoxValue_TextChanged);
		this.label3.AutoSize = true;
		this.label3.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label3.Location = new System.Drawing.Point(3, 52);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(79, 26);
		this.label3.TabIndex = 4;
		this.label3.Text = "skillGroup";
		this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label4.AutoSize = true;
		this.label4.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label4.Location = new System.Drawing.Point(3, 78);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(79, 26);
		this.label4.TabIndex = 5;
		this.label4.Text = "skillClass";
		this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label5.AutoSize = true;
		this.label5.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label5.Location = new System.Drawing.Point(3, 104);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(79, 26);
		this.label5.TabIndex = 6;
		this.label5.Text = "skillStep";
		this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label6.AutoSize = true;
		this.label6.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label6.Location = new System.Drawing.Point(3, 130);
		this.label6.Name = "label6";
		this.label6.Size = new System.Drawing.Size(79, 26);
		this.label6.TabIndex = 7;
		this.label6.Text = "skillLevel";
		this.label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label7.AutoSize = true;
		this.label7.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label7.Location = new System.Drawing.Point(3, 156);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(79, 26);
		this.label7.TabIndex = 8;
		this.label7.Text = "abilityID";
		this.label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label8.AutoSize = true;
		this.label8.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label8.Location = new System.Drawing.Point(3, 182);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(79, 26);
		this.label8.TabIndex = 9;
		this.label8.Text = "questID";
		this.label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxSkillGroup.Location = new System.Drawing.Point(88, 55);
		this.textBoxSkillGroup.Name = "textBoxSkillGroup";
		this.textBoxSkillGroup.Size = new System.Drawing.Size(100, 20);
		this.textBoxSkillGroup.TabIndex = 10;
		this.textBoxSkillGroup.TextChanged += new System.EventHandler(textBoxSkillGroup_TextChanged);
		this.textBoxSkillClass.Location = new System.Drawing.Point(88, 81);
		this.textBoxSkillClass.Name = "textBoxSkillClass";
		this.textBoxSkillClass.Size = new System.Drawing.Size(100, 20);
		this.textBoxSkillClass.TabIndex = 11;
		this.textBoxSkillClass.TextChanged += new System.EventHandler(textBoxSkillClass_TextChanged);
		this.textBoxSkillStep.Location = new System.Drawing.Point(88, 107);
		this.textBoxSkillStep.Name = "textBoxSkillStep";
		this.textBoxSkillStep.Size = new System.Drawing.Size(100, 20);
		this.textBoxSkillStep.TabIndex = 12;
		this.textBoxSkillStep.TextChanged += new System.EventHandler(textBoxSkillStep_TextChanged);
		this.textBoxSkillLevel.Location = new System.Drawing.Point(88, 133);
		this.textBoxSkillLevel.Name = "textBoxSkillLevel";
		this.textBoxSkillLevel.Size = new System.Drawing.Size(100, 20);
		this.textBoxSkillLevel.TabIndex = 13;
		this.textBoxSkillLevel.TextChanged += new System.EventHandler(textBoxSkillLevel_TextChanged);
		this.textBoxAbilityID.Location = new System.Drawing.Point(88, 159);
		this.textBoxAbilityID.Name = "textBoxAbilityID";
		this.textBoxAbilityID.Size = new System.Drawing.Size(100, 20);
		this.textBoxAbilityID.TabIndex = 14;
		this.textBoxAbilityID.TextChanged += new System.EventHandler(textBoxAbilityID_TextChanged);
		this.textBoxQuestID.Location = new System.Drawing.Point(88, 185);
		this.textBoxQuestID.Name = "textBoxQuestID";
		this.textBoxQuestID.Size = new System.Drawing.Size(100, 20);
		this.textBoxQuestID.TabIndex = 15;
		this.textBoxQuestID.TextChanged += new System.EventHandler(textBoxQuestID_TextChanged);
		this.label9.AutoSize = true;
		this.label9.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label9.Location = new System.Drawing.Point(3, 208);
		this.label9.Name = "label9";
		this.label9.Size = new System.Drawing.Size(79, 26);
		this.label9.TabIndex = 16;
		this.label9.Text = "clan";
		this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxClan.Location = new System.Drawing.Point(88, 211);
		this.textBoxClan.Name = "textBoxClan";
		this.textBoxClan.Size = new System.Drawing.Size(100, 20);
		this.textBoxClan.TabIndex = 17;
		this.textBoxClan.TextChanged += new System.EventHandler(textBoxClan_TextChanged);
		this.label10.AutoSize = true;
		this.label10.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label10.Location = new System.Drawing.Point(3, 234);
		this.label10.Name = "label10";
		this.label10.Size = new System.Drawing.Size(79, 26);
		this.label10.TabIndex = 18;
		this.label10.Text = "characGrade";
		this.label10.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxCharacGrade.Location = new System.Drawing.Point(88, 237);
		this.textBoxCharacGrade.Name = "textBoxCharacGrade";
		this.textBoxCharacGrade.Size = new System.Drawing.Size(100, 20);
		this.textBoxCharacGrade.TabIndex = 19;
		this.textBoxCharacGrade.TextChanged += new System.EventHandler(textBoxCharacGrade_TextChanged);
		this.labelClass.AutoSize = true;
		this.labelClass.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelClass.Location = new System.Drawing.Point(3, 260);
		this.labelClass.Name = "labelClass";
		this.labelClass.Size = new System.Drawing.Size(79, 26);
		this.labelClass.TabIndex = 20;
		this.labelClass.Text = "class";
		this.labelClass.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxClass.Location = new System.Drawing.Point(88, 263);
		this.textBoxClass.Name = "textBoxClass";
		this.textBoxClass.Size = new System.Drawing.Size(100, 20);
		this.textBoxClass.TabIndex = 21;
		this.textBoxClass.TextChanged += new System.EventHandler(textBoxClass_TextChanged);
		this.textBoxPrice.Location = new System.Drawing.Point(88, 289);
		this.textBoxPrice.Name = "textBoxPrice";
		this.textBoxPrice.Size = new System.Drawing.Size(100, 20);
		this.textBoxPrice.TabIndex = 23;
		this.textBoxPrice.TextChanged += new System.EventHandler(textBoxPrice_TextChanged);
		this.unknownByteIndex.Location = new System.Drawing.Point(88, 315);
		this.unknownByteIndex.Name = "unknownByteIndex";
		this.unknownByteIndex.Size = new System.Drawing.Size(121, 21);
		this.unknownByteIndex.TabIndex = 0;
		this.unknownByteIndex.SelectedIndexChanged += new System.EventHandler(unknownByteIndex_SelectedIndexChanged);
		this.unknownByteValue.Location = new System.Drawing.Point(217, 315);
		this.unknownByteValue.Name = "unknownByteValue";
		this.unknownByteValue.Size = new System.Drawing.Size(100, 20);
		this.unknownByteValue.TabIndex = 26;
		this.unknownByteValue.TextChanged += new System.EventHandler(unknownByteValue_TextChanged);
		this.btn_pasteItem.Enabled = false;
		this.btn_pasteItem.Location = new System.Drawing.Point(88, 407);
		this.btn_pasteItem.Name = "btn_pasteItem";
		this.btn_pasteItem.Size = new System.Drawing.Size(123, 23);
		this.btn_pasteItem.TabIndex = 57;
		this.btn_pasteItem.Text = "Paste Item";
		this.btn_pasteItem.UseVisualStyleBackColor = false;
		this.btn_pasteItem.Click += new System.EventHandler(btn_pasteItem_Click);
		this.btn_copyItem.Location = new System.Drawing.Point(88, 437);
		this.btn_copyItem.Name = "btn_copyItem";
		this.btn_copyItem.Size = new System.Drawing.Size(123, 23);
		this.btn_copyItem.TabIndex = 56;
		this.btn_copyItem.Text = "Copy Item";
		this.btn_copyItem.UseVisualStyleBackColor = false;
		this.btn_copyItem.Click += new System.EventHandler(btn_copyItem_Click);
		this.label13.AutoSize = true;
		this.label13.Location = new System.Drawing.Point(3, 339);
		this.label13.Name = "label13";
		this.label13.Size = new System.Drawing.Size(56, 13);
		this.label13.TabIndex = 25;
		this.label13.Text = "Item Rank";
		this.label13.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxItemRank.Location = new System.Drawing.Point(88, 342);
		this.textBoxItemRank.Name = "textBoxItemRank";
		this.textBoxItemRank.Size = new System.Drawing.Size(100, 20);
		this.textBoxItemRank.TabIndex = 58;
		this.textBoxItemRank.TextChanged += new System.EventHandler(textBoxItemRank_TextChanged);
		this.tabPageUnknown.Location = new System.Drawing.Point(4, 22);
		this.tabPageUnknown.Name = "tabPageUnknown";
		this.tabPageUnknown.Padding = new System.Windows.Forms.Padding(3);
		this.tabPageUnknown.Size = new System.Drawing.Size(368, 465);
		this.tabPageUnknown.TabIndex = 1;
		this.tabPageUnknown.Text = "Unknown bytes";
		this.tabPageUnknown.UseVisualStyleBackColor = true;
		this.itemBookBindingSource.DataSource = typeof(ItemTableReader.ItemBook);
		this.label14.AutoSize = true;
		this.label14.Location = new System.Drawing.Point(3, 370);
		this.label14.Name = "label14";
		this.label14.Size = new System.Drawing.Size(58, 13);
		this.label14.TabIndex = 59;
		this.label14.Text = "XSD Index";
		this.label14.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxXSDIndex.Location = new System.Drawing.Point(87, 372);
		this.textBoxXSDIndex.Margin = new System.Windows.Forms.Padding(2);
		this.textBoxXSDIndex.Name = "textBoxXSDIndex";
		this.textBoxXSDIndex.Size = new System.Drawing.Size(82, 20);
		this.textBoxXSDIndex.TabIndex = 66;
		this.textBoxXSDIndex.TextChanged += new System.EventHandler(textBoxXSDIndex_TextChanged);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(550, 527);
		base.Controls.Add(this.tabControl1);
		base.Controls.Add(this.listBoxBooks);
		base.MaximizeBox = false;
		base.MinimizeBox = false;
		base.Name = "FormBooks";
		base.ShowIcon = false;
		this.Text = "Books Editor";
		base.Load += new System.EventHandler(FormBooks_Load);
		this.tabControl1.ResumeLayout(false);
		this.tabPage1.ResumeLayout(false);
		this.tableLayoutPanel1.ResumeLayout(false);
		this.tableLayoutPanel1.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.itemBookBindingSource).EndInit();
		base.ResumeLayout(false);
	}
}
