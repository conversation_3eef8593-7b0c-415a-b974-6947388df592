{"version": 2, "dgSpecHash": "WKTrqKt5X7A=", "success": false, "projectFilePath": "D:\\SERVER\\工具源码\\item\\ItemTableReader.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\system.resources.extensions\\8.0.0\\system.resources.extensions.8.0.0.nupkg.sha512"], "logs": [{"code": "NU1202", "level": "Error", "message": "包 System.Resources.Extensions 8.0.0 与 net45 (.NETFramework,Version=v4.5) 不兼容。 包 System.Resources.Extensions 8.0.0 支持:\r\n  - net462 (.NETFramework,Version=v4.6.2)\r\n  - net6.0 (.NETCoreApp,Version=v6.0)\r\n  - net7.0 (.NETCoreApp,Version=v7.0)\r\n  - net8.0 (.NETCoreApp,Version=v8.0)\r\n  - netstandard2.0 (.NETStandard,Version=v2.0)", "libraryId": "System.Resources.Extensions", "targetGraphs": [".NETFramework,Version=v4.5"]}, {"code": "NU1202", "level": "Error", "message": "包 System.Resources.Extensions 8.0.0 与 net45 (.NETFramework,Version=v4.5) / win-x86 不兼容。 包 System.Resources.Extensions 8.0.0 支持:\r\n  - net462 (.NETFramework,Version=v4.6.2)\r\n  - net6.0 (.NETCoreApp,Version=v6.0)\r\n  - net7.0 (.NETCoreApp,Version=v7.0)\r\n  - net8.0 (.NETCoreApp,Version=v8.0)\r\n  - netstandard2.0 (.NETStandard,Version=v2.0)", "libraryId": "System.Resources.Extensions", "targetGraphs": [".NETFramework,Version=v4.5/win-x86"]}]}