using System.IO;

namespace ItemTableReader;

internal class ItemMoney : ItemBase
{
	public static Map<ItemMoney> Moneys = new Map<ItemMoney>();

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(89));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 89);
		num += 89;
	}
}
