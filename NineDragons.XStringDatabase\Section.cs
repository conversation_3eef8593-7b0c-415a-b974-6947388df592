using System.ComponentModel;
using System.Text;

namespace NineDragons.XStringDatabase;

public class Section
{
	public const int NAME_MAXLEN = 64;

	public int XStringCount;

	protected byte[] _Name;

	public XStringCollection XStrings = new XStringCollection();

	public byte[] Name
	{
		get
		{
			return _Name;
		}
		set
		{
			_Name = ByteArrayToFixedByteArray(value, 128);
		}
	}

	public string UnicodeName
	{
		get
		{
			return Encoding.Unicode.GetString(Name);
		}
		set
		{
			Name = StringToFixedUnicodeByteArray(value, 128);
		}
	}

	public Section()
	{
		BindEvents();
	}

	public Section(int count, byte[] name)
		: this()
	{
		XStringCount = count;
		Name = name;
	}

	private void BindEvents()
	{
		XStrings.Rows.ListChanged += RowListChangedEventHandler;
	}

	public bool NameEqualsTo(byte[] CompareName)
	{
		if (Name == null && CompareName == null)
		{
			return true;
		}
		if (Name == null && CompareName != null)
		{
			return false;
		}
		if (CompareName == null && Name != null)
		{
			return false;
		}
		if (Name.Length != CompareName.Length)
		{
			return false;
		}
		if (Name.GetHashCode() == CompareName.GetHashCode())
		{
			return true;
		}
		int i = 0;
		for (int num = Name.Length; i < num; i++)
		{
			if (Name[i] != CompareName[i])
			{
				return false;
			}
		}
		return true;
	}

	protected byte[] ByteArrayToFixedByteArray(byte[] value, int size)
	{
		byte[] array = new byte[size];
		int num = value.Length;
		for (int i = 0; i < num && i < size; i++)
		{
			array[i] = value[i];
		}
		return array;
	}

	protected byte[] StringToFixedUnicodeByteArray(string value, int size)
	{
		byte[] bytes = Encoding.Unicode.GetBytes(value);
		byte[] array = new byte[size];
		int num = bytes.Length;
		for (int i = 0; i < num && i < size; i++)
		{
			array[i] = bytes[i];
		}
		return array;
	}

	public override string ToString()
	{
		return UnicodeName;
	}

	private void RowListChangedEventHandler(object sender, ListChangedEventArgs e)
	{
		XStringCount = XStrings.Rows.Count;
	}
}
