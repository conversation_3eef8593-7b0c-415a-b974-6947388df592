using System.ComponentModel.DataAnnotations;

namespace ItemTableReader;

internal enum FameValues
{
	[Display(Name = "None")]
	NONE = 0,
	[Display(Name = "Brave Heart")]
	GK_TITLE1 = 1,
	[Display(Name = "Noble Soul")]
	GK_TITLE2 = 2,
	[Display(Name = "Peaceful Warrior")]
	GK_TITLE3 = 3,
	[Display(Name = "Awakened Soul")]
	GK_TITLE4 = 4,
	[<PERSON>splay(Name = "Hero of The Land")]
	GK_TITLE5 = 5,
	[Display(Name = "Selfless One")]
	GK_TITLE6 = 6,
	[Display(Name = "Blessed by Fate")]
	GK_TITLE7 = 7,
	[Display(Name = "Destiny's Hand")]
	GK_TITLE8 = 8,
	[Display(Name = "Dweller on The Threshold")]
	GK_TITLE9 = 9,
	[Display(Name = "Seeker of Atonement")]
	GK_TITLE10 = 10,
	[Display(Name = "Good Karma")]
	GOOD_KARMA = 99,
	[Display(Name = "Dark Heart")]
	BK_TITLE1 = 101,
	[Display(Name = "Ignoble Soul")]
	BK_TITLE2 = 102,
	[Display(Name = "Savage Warrior")]
	BK_TITLE3 = 103,
	[Display(Name = "Captive Soul")]
	BK_TITLE4 = 104,
	[Display(Name = "Scar of The Land")]
	BK_TITLE5 = 105,
	[Display(Name = "Selfish Fiend")]
	BK_TITLE6 = 106,
	[Display(Name = "Cursed by Fate")]
	BK_TITLE7 = 107,
	[Display(Name = "Hell’s Pawn")]
	BK_TITLE8 = 108,
	[Display(Name = "Demon on The Threshold")]
	BK_TITLE9 = 109,
	[Display(Name = "Minion of The Damned")]
	BK_TITLE10 = 110,
	[Display(Name = "Bad Karma")]
	BAD_KARMA = 199
}
