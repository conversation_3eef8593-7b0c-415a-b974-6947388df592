using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;
using ItemTableReader.Forms;

namespace ItemTableReader;

public class FormMain : Form
{
	private ItemTableHeader header = new ItemTableHeader();

	private IContainer components;

	private Button btnWep;

	private Button btnCloth;

	private Button btnElixirs;

	private Button btnAccessories;

	private Button btnPasses;

	private Button btnBooks;

	private Button btnResources;

	private TextBox textBoxFindItemRank;

	private Button btnFindItem;

	private Button btnAllRanks;

	private Button btnLifes;

	private Button btnSave;

	private Button btnPotions;

	private GroupBox groupBox1;

	private Button btnSocket;

	private ProgressBar progressBar1;

	public FormMain()
	{
		InitializeComponent();
		Version version = Assembly.GetExecutingAssembly().GetName().Version;
		DateTime dateTime = new DateTime(2000, 1, 1).AddDays(version.Build).AddSeconds(version.Revision * 2);
		Text += $" {version} ({dateTime})";
	}

	private void btnWep_Click(object sender, EventArgs e)
	{
		new FormWeapons().Show(this);
	}

	private void btnCloth_Click(object sender, EventArgs e)
	{
		new FormClothes().Show(this);
	}

	private void btnElixirs_Click(object sender, EventArgs e)
	{
		new FormElixirs().Show(this);
	}

	private void btnAccessories_Click(object sender, EventArgs e)
	{
		new FormAccessories().Show(this);
	}

	private void btnPasses_Click(object sender, EventArgs e)
	{
		new FormPasses().Show(this);
	}

	private void btnBooks_Click(object sender, EventArgs e)
	{
		new FormBooks().Show(this);
	}

	private void btnResources_Click(object sender, EventArgs e)
	{
		new FormResources().Show(this);
	}

	private void btnFindItem_Click(object sender, EventArgs e)
	{
		try
		{
			ushort key = Convert.ToUInt16(textBoxFindItemRank.Text);
			string text = "";
			if (ItemParser.ItemRanks.ContainsKey(key))
			{
				foreach (ItemBase item in ItemParser.ItemRanks[key])
				{
					text = text + item.FullName + "\n";
				}
			}
			else
			{
				text = "Not found";
			}
			MessageBox.Show(text);
		}
		catch
		{
			MessageBox.Show("Invalid Input");
		}
	}

	private void btnAllRanks_Click(object sender, EventArgs e)
	{
		new FormItemRanks().Show(this);
	}

	private void btnLifes_Click(object sender, EventArgs e)
	{
		new FormLifes().Show(this);
	}

	private void btnSave_Click(object sender, EventArgs e)
	{
		SaveFileDialog saveFileDialog = new SaveFileDialog();
		if (saveFileDialog.ShowDialog() == DialogResult.OK)
		{
			header.Save(saveFileDialog.OpenFile());
		}
	}

	private void btnPotions_Click(object sender, EventArgs e)
	{
		new FormPotion().Show(this);
	}

	private void btnSocket_Click(object sender, EventArgs e)
	{
		MessageBox.Show("Socket 功能正在开发中，敬请期待！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
	}

	private async void FormMain_Shown(object sender, EventArgs e)
	{
		for (int i = 0; i < base.Controls.Count; i++)
		{
			base.Controls[i].Enabled = false;
		}
		_ = DateTimeOffset.Now;
		try
		{
			string path = "item_table.bit";
			if (!File.Exists(path))
			{
				OpenFileDialog openFileDialog = new OpenFileDialog();
				openFileDialog.Filter = "Item table (*.bit)|*.bit|All files (*.*)|*.*";
				openFileDialog.FilterIndex = 1;
				if (openFileDialog.ShowDialog() == DialogResult.OK)
				{
					path = openFileDialog.FileName;
				}
			}
			Stream str = File.Open(path, FileMode.Open);
			try
			{
				await Task.Run(delegate
				{
					header.Load(str);
				});
				progressBar1.Visible = false;
				for (int j = 0; j < base.Controls.Count; j++)
				{
					base.Controls[j].Enabled = true;
				}
			}
			finally
			{
				if (str != null)
				{
					((IDisposable)str).Dispose();
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(this, ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.btnWep = new System.Windows.Forms.Button();
		this.btnCloth = new System.Windows.Forms.Button();
		this.btnElixirs = new System.Windows.Forms.Button();
		this.btnAccessories = new System.Windows.Forms.Button();
		this.btnPasses = new System.Windows.Forms.Button();
		this.btnBooks = new System.Windows.Forms.Button();
		this.btnResources = new System.Windows.Forms.Button();
		this.textBoxFindItemRank = new System.Windows.Forms.TextBox();
		this.btnFindItem = new System.Windows.Forms.Button();
		this.btnAllRanks = new System.Windows.Forms.Button();
		this.btnLifes = new System.Windows.Forms.Button();
		this.btnSave = new System.Windows.Forms.Button();
		this.btnPotions = new System.Windows.Forms.Button();
		this.groupBox1 = new System.Windows.Forms.GroupBox();
		this.btnSocket = new System.Windows.Forms.Button();
		this.progressBar1 = new System.Windows.Forms.ProgressBar();
		this.groupBox1.SuspendLayout();
		base.SuspendLayout();
		this.btnWep.Location = new System.Drawing.Point(14, 13);
		this.btnWep.Name = "btnWep";
		this.btnWep.Size = new System.Drawing.Size(75, 23);
		this.btnWep.TabIndex = 0;
		this.btnWep.Text = "Weapons";
		this.btnWep.UseVisualStyleBackColor = true;
		this.btnWep.Click += new System.EventHandler(btnWep_Click);
		this.btnCloth.Location = new System.Drawing.Point(14, 41);
		this.btnCloth.Name = "btnCloth";
		this.btnCloth.Size = new System.Drawing.Size(75, 23);
		this.btnCloth.TabIndex = 1;
		this.btnCloth.Text = "Clothes";
		this.btnCloth.UseVisualStyleBackColor = true;
		this.btnCloth.Click += new System.EventHandler(btnCloth_Click);
		this.btnElixirs.Location = new System.Drawing.Point(14, 69);
		this.btnElixirs.Name = "btnElixirs";
		this.btnElixirs.Size = new System.Drawing.Size(75, 23);
		this.btnElixirs.TabIndex = 2;
		this.btnElixirs.Text = "Elixirs";
		this.btnElixirs.UseVisualStyleBackColor = true;
		this.btnElixirs.Click += new System.EventHandler(btnElixirs_Click);
		this.btnAccessories.Location = new System.Drawing.Point(14, 97);
		this.btnAccessories.Name = "btnAccessories";
		this.btnAccessories.Size = new System.Drawing.Size(75, 23);
		this.btnAccessories.TabIndex = 3;
		this.btnAccessories.Text = "Accessories";
		this.btnAccessories.UseVisualStyleBackColor = true;
		this.btnAccessories.Click += new System.EventHandler(btnAccessories_Click);
		this.btnPasses.Enabled = false;
		this.btnPasses.Location = new System.Drawing.Point(94, 125);
		this.btnPasses.Name = "btnPasses";
		this.btnPasses.Size = new System.Drawing.Size(75, 23);
		this.btnPasses.TabIndex = 4;
		this.btnPasses.Text = "Passes";
		this.btnPasses.UseVisualStyleBackColor = true;
		this.btnPasses.Click += new System.EventHandler(btnPasses_Click);
		this.btnBooks.Location = new System.Drawing.Point(94, 13);
		this.btnBooks.Name = "btnBooks";
		this.btnBooks.Size = new System.Drawing.Size(75, 23);
		this.btnBooks.TabIndex = 5;
		this.btnBooks.Text = "Books";
		this.btnBooks.UseVisualStyleBackColor = true;
		this.btnBooks.Click += new System.EventHandler(btnBooks_Click);
		this.btnResources.Location = new System.Drawing.Point(94, 41);
		this.btnResources.Name = "btnResources";
		this.btnResources.Size = new System.Drawing.Size(75, 23);
		this.btnResources.TabIndex = 6;
		this.btnResources.Text = "Resources";
		this.btnResources.UseVisualStyleBackColor = true;
		this.btnResources.Click += new System.EventHandler(btnResources_Click);
		this.textBoxFindItemRank.Location = new System.Drawing.Point(5, 56);
		this.textBoxFindItemRank.Name = "textBoxFindItemRank";
		this.textBoxFindItemRank.Size = new System.Drawing.Size(73, 20);
		this.textBoxFindItemRank.TabIndex = 7;
		this.btnFindItem.Location = new System.Drawing.Point(5, 82);
		this.btnFindItem.Name = "btnFindItem";
		this.btnFindItem.Size = new System.Drawing.Size(72, 23);
		this.btnFindItem.TabIndex = 8;
		this.btnFindItem.Text = "Find item rank";
		this.btnFindItem.UseVisualStyleBackColor = true;
		this.btnFindItem.Click += new System.EventHandler(btnFindItem_Click);
		this.btnAllRanks.Location = new System.Drawing.Point(5, 18);
		this.btnAllRanks.Name = "btnAllRanks";
		this.btnAllRanks.Size = new System.Drawing.Size(75, 23);
		this.btnAllRanks.TabIndex = 9;
		this.btnAllRanks.Text = "All ranks";
		this.btnAllRanks.UseVisualStyleBackColor = true;
		this.btnAllRanks.Click += new System.EventHandler(btnAllRanks_Click);
		this.btnLifes.Location = new System.Drawing.Point(94, 69);
		this.btnLifes.Name = "btnLifes";
		this.btnLifes.Size = new System.Drawing.Size(75, 23);
		this.btnLifes.TabIndex = 10;
		this.btnLifes.Text = "Lifes";
		this.btnLifes.UseVisualStyleBackColor = true;
		this.btnLifes.Click += new System.EventHandler(btnLifes_Click);
		this.btnSave.Dock = System.Windows.Forms.DockStyle.Right;
		this.btnSave.Font = new System.Drawing.Font("Microsoft Sans Serif", 13.8f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.btnSave.Location = new System.Drawing.Point(285, 0);
		this.btnSave.Name = "btnSave";
		this.btnSave.Size = new System.Drawing.Size(69, 169);
		this.btnSave.TabIndex = 11;
		this.btnSave.Text = "Save Item Table";
		this.btnSave.UseVisualStyleBackColor = true;
		this.btnSave.Click += new System.EventHandler(btnSave_Click);
		this.btnPotions.Location = new System.Drawing.Point(14, 125);
		this.btnPotions.Name = "btnPotions";
		this.btnPotions.Size = new System.Drawing.Size(75, 23);
		this.btnPotions.TabIndex = 12;
		this.btnPotions.Text = "Potions";
		this.btnPotions.UseVisualStyleBackColor = true;
		this.btnPotions.Click += new System.EventHandler(btnPotions_Click);
		this.groupBox1.Controls.Add(this.btnAllRanks);
		this.groupBox1.Controls.Add(this.textBoxFindItemRank);
		this.groupBox1.Controls.Add(this.btnFindItem);
		this.groupBox1.Location = new System.Drawing.Point(188, 11);
		this.groupBox1.Margin = new System.Windows.Forms.Padding(2);
		this.groupBox1.Name = "groupBox1";
		this.groupBox1.Padding = new System.Windows.Forms.Padding(2);
		this.groupBox1.Size = new System.Drawing.Size(82, 115);
		this.groupBox1.TabIndex = 13;
		this.groupBox1.TabStop = false;
		this.groupBox1.Text = "Item Ranks";
		this.btnSocket.Location = new System.Drawing.Point(94, 97);
		this.btnSocket.Margin = new System.Windows.Forms.Padding(2);
		this.btnSocket.Name = "btnSocket";
		this.btnSocket.Size = new System.Drawing.Size(75, 23);
		this.btnSocket.TabIndex = 14;
		this.btnSocket.Text = "Socket";
		this.btnSocket.UseVisualStyleBackColor = true;
		this.btnSocket.Click += new System.EventHandler(btnSocket_Click);
		this.progressBar1.Location = new System.Drawing.Point(188, 132);
		this.progressBar1.Name = "progressBar1";
		this.progressBar1.Size = new System.Drawing.Size(82, 25);
		this.progressBar1.Style = System.Windows.Forms.ProgressBarStyle.Marquee;
		this.progressBar1.TabIndex = 15;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(354, 169);
		base.Controls.Add(this.progressBar1);
		base.Controls.Add(this.btnSocket);
		base.Controls.Add(this.groupBox1);
		base.Controls.Add(this.btnPotions);
		base.Controls.Add(this.btnSave);
		base.Controls.Add(this.btnLifes);
		base.Controls.Add(this.btnResources);
		base.Controls.Add(this.btnBooks);
		base.Controls.Add(this.btnPasses);
		base.Controls.Add(this.btnAccessories);
		base.Controls.Add(this.btnElixirs);
		base.Controls.Add(this.btnCloth);
		base.Controls.Add(this.btnWep);
		base.MaximizeBox = false;
		base.MinimizeBox = false;
		base.Name = "FormMain";
		base.ShowIcon = false;
		this.Text = "物品修改工具";
		base.Shown += new System.EventHandler(FormMain_Shown);
		this.groupBox1.ResumeLayout(false);
		this.groupBox1.PerformLayout();
		base.ResumeLayout(false);
	}
}
