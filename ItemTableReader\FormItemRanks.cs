using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormItemRanks : Form
{
	private IContainer components;

	private SplitContainer splitContainer1;

	private ListBox listBoxRanks;

	private DataGridView dataGridView1;

	private BindingSource itemBaseBindingSource;

	private DataGridViewTextBoxColumn itemRankDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn fullNameDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn typeDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn iDDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn nameDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn secondTypeDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn modelIndexDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn iconIndexDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn gradeDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn qualityDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn quality2DataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn priceDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn descriptionDataGridViewTextBoxColumn;

	private DataGridViewCheckBoxColumn canDropDataGridViewCheckBoxColumn;

	private DataGridViewCheckBoxColumn canStoreDataGridViewCheckBoxColumn;

	private DataGridViewCheckBoxColumn canNPCDataGridViewCheckBoxColumn;

	private DataGridViewCheckBoxColumn canTradeDataGridViewCheckBoxColumn;

	private DataGridViewTextBoxColumn cashCheckDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn timeDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn applyClanDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn contrib1DataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn contrib2DataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn fameDataGridViewTextBoxColumn;

	private Button btnExport;

	public FormItemRanks()
	{
		InitializeComponent();
	}

	private void listBoxRanks_SelectedIndexChanged(object sender, EventArgs e)
	{
		ushort key = Convert.ToUInt16(listBoxRanks.SelectedItem);
		dataGridView1.DataSource = ItemParser.ItemRanks[key];
	}

	private void FormItemRanks_Load(object sender, EventArgs e)
	{
		listBoxRanks.BeginUpdate();
		foreach (ushort item in from i in ItemParser.ItemRanks.Keys.ToArray()
			orderby i
			select i)
		{
			listBoxRanks.Items.Add(item);
		}
		listBoxRanks.EndUpdate();
	}

	private void btnExport_Click(object sender, EventArgs e)
	{
		try
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*";
			saveFileDialog.FilterIndex = 1;
			saveFileDialog.FileName = "ItemRanks_Export.csv";

			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				ExportToCSV(saveFileDialog.FileName);
				MessageBox.Show("导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show($"导出失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
		}
	}

	private void ExportToCSV(string fileName)
	{
		using (StreamWriter writer = new StreamWriter(fileName, false, Encoding.UTF8))
		{
			// Write header
			writer.WriteLine("ItemRank,ID,Name,Type,SecondType,ModelIndex,IconIndex,Grade,Quality,Quality2,Price,Description,CanDrop,CanStore,CanNPC,CanTrade,CashCheck,Time,ApplyClan,Contrib1,Contrib2,Fame");

			// Write data for all ranks
			foreach (ushort rank in ItemParser.ItemRanks.Keys.OrderBy(k => k))
			{
				foreach (ItemBase item in ItemParser.ItemRanks[rank])
				{
					// Use the rank from the dictionary key to ensure correct ItemRank value
					writer.WriteLine($"{rank},{item.ID},\"{EscapeCSV(item.Name)}\",{item.Type},{item.SecondType},{item.ModelIndex},{item.IconIndex},{item.Grade},{item.Quality},{item.Quality2},{item.Price},\"{EscapeCSV(item.Description)}\",{item.CanDrop},{item.CanStore},{item.CanNPC},{item.CanTrade},{item.CashCheck},{item.Time},{item.ApplyClan},{item.Contrib1},{item.Contrib2},{item.Fame}");
				}
			}
		}
	}

	private string EscapeCSV(string value)
	{
		if (string.IsNullOrEmpty(value))
			return "";

		// Escape quotes by doubling them
		return value.Replace("\"", "\"\"");
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.splitContainer1 = new System.Windows.Forms.SplitContainer();
		this.listBoxRanks = new System.Windows.Forms.ListBox();
		this.dataGridView1 = new System.Windows.Forms.DataGridView();
		this.btnExport = new System.Windows.Forms.Button();
		this.itemRankDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.fullNameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.typeDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.iDDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.nameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.secondTypeDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.modelIndexDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.iconIndexDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.gradeDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.qualityDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.quality2DataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.priceDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.descriptionDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.canDropDataGridViewCheckBoxColumn = new System.Windows.Forms.DataGridViewCheckBoxColumn();
		this.canStoreDataGridViewCheckBoxColumn = new System.Windows.Forms.DataGridViewCheckBoxColumn();
		this.canNPCDataGridViewCheckBoxColumn = new System.Windows.Forms.DataGridViewCheckBoxColumn();
		this.canTradeDataGridViewCheckBoxColumn = new System.Windows.Forms.DataGridViewCheckBoxColumn();
		this.cashCheckDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.timeDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.applyClanDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.contrib1DataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.contrib2DataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.fameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.itemBaseBindingSource = new System.Windows.Forms.BindingSource(this.components);
		((System.ComponentModel.ISupportInitialize)this.splitContainer1).BeginInit();
		this.splitContainer1.Panel1.SuspendLayout();
		this.splitContainer1.Panel2.SuspendLayout();
		this.splitContainer1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.dataGridView1).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.itemBaseBindingSource).BeginInit();
		base.SuspendLayout();
		this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.splitContainer1.Location = new System.Drawing.Point(0, 0);
		this.splitContainer1.Name = "splitContainer1";
		this.splitContainer1.Panel1.Controls.Add(this.listBoxRanks);
		this.splitContainer1.Panel1.Controls.Add(this.btnExport);
		this.splitContainer1.Panel2.Controls.Add(this.dataGridView1);
		this.splitContainer1.Size = new System.Drawing.Size(759, 344);
		this.splitContainer1.SplitterDistance = 121;
		this.splitContainer1.TabIndex = 0;
		this.listBoxRanks.Dock = System.Windows.Forms.DockStyle.Top;
		this.listBoxRanks.FormattingEnabled = true;
		this.listBoxRanks.Location = new System.Drawing.Point(0, 0);
		this.listBoxRanks.Name = "listBoxRanks";
		this.listBoxRanks.Size = new System.Drawing.Size(121, 300);
		this.listBoxRanks.TabIndex = 0;
		this.listBoxRanks.SelectedIndexChanged += new System.EventHandler(listBoxRanks_SelectedIndexChanged);
		this.btnExport.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.btnExport.Location = new System.Drawing.Point(0, 300);
		this.btnExport.Name = "btnExport";
		this.btnExport.Size = new System.Drawing.Size(121, 44);
		this.btnExport.TabIndex = 1;
		this.btnExport.Text = "导出信息";
		this.btnExport.UseVisualStyleBackColor = true;
		this.btnExport.Click += new System.EventHandler(btnExport_Click);
		this.dataGridView1.AllowUserToAddRows = false;
		this.dataGridView1.AllowUserToDeleteRows = false;
		this.dataGridView1.AutoGenerateColumns = false;
		this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.dataGridView1.Columns.AddRange(this.itemRankDataGridViewTextBoxColumn, this.fullNameDataGridViewTextBoxColumn, this.typeDataGridViewTextBoxColumn, this.iDDataGridViewTextBoxColumn, this.nameDataGridViewTextBoxColumn, this.secondTypeDataGridViewTextBoxColumn, this.modelIndexDataGridViewTextBoxColumn, this.iconIndexDataGridViewTextBoxColumn, this.gradeDataGridViewTextBoxColumn, this.qualityDataGridViewTextBoxColumn, this.quality2DataGridViewTextBoxColumn, this.priceDataGridViewTextBoxColumn, this.descriptionDataGridViewTextBoxColumn, this.canDropDataGridViewCheckBoxColumn, this.canStoreDataGridViewCheckBoxColumn, this.canNPCDataGridViewCheckBoxColumn, this.canTradeDataGridViewCheckBoxColumn, this.cashCheckDataGridViewTextBoxColumn, this.timeDataGridViewTextBoxColumn, this.applyClanDataGridViewTextBoxColumn, this.contrib1DataGridViewTextBoxColumn, this.contrib2DataGridViewTextBoxColumn, this.fameDataGridViewTextBoxColumn);
		this.dataGridView1.DataSource = this.itemBaseBindingSource;
		this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.dataGridView1.Location = new System.Drawing.Point(0, 0);
		this.dataGridView1.Name = "dataGridView1";
		this.dataGridView1.Size = new System.Drawing.Size(634, 344);
		this.dataGridView1.TabIndex = 0;
		this.itemRankDataGridViewTextBoxColumn.DataPropertyName = "ItemRank";
		this.itemRankDataGridViewTextBoxColumn.HeaderText = "ItemRank";
		this.itemRankDataGridViewTextBoxColumn.Name = "itemRankDataGridViewTextBoxColumn";
		this.itemRankDataGridViewTextBoxColumn.ReadOnly = true;
		this.itemRankDataGridViewTextBoxColumn.Visible = false;
		this.fullNameDataGridViewTextBoxColumn.DataPropertyName = "FullName";
		this.fullNameDataGridViewTextBoxColumn.HeaderText = "FullName";
		this.fullNameDataGridViewTextBoxColumn.Name = "fullNameDataGridViewTextBoxColumn";
		this.fullNameDataGridViewTextBoxColumn.ReadOnly = true;
		this.fullNameDataGridViewTextBoxColumn.Visible = false;
		this.typeDataGridViewTextBoxColumn.DataPropertyName = "Type";
		this.typeDataGridViewTextBoxColumn.HeaderText = "Type";
		this.typeDataGridViewTextBoxColumn.Name = "typeDataGridViewTextBoxColumn";
		this.typeDataGridViewTextBoxColumn.ReadOnly = true;
		this.iDDataGridViewTextBoxColumn.DataPropertyName = "ID";
		this.iDDataGridViewTextBoxColumn.HeaderText = "ID";
		this.iDDataGridViewTextBoxColumn.Name = "iDDataGridViewTextBoxColumn";
		this.iDDataGridViewTextBoxColumn.ReadOnly = true;
		this.nameDataGridViewTextBoxColumn.DataPropertyName = "Name";
		this.nameDataGridViewTextBoxColumn.HeaderText = "Name";
		this.nameDataGridViewTextBoxColumn.Name = "nameDataGridViewTextBoxColumn";
		this.nameDataGridViewTextBoxColumn.ReadOnly = true;
		this.secondTypeDataGridViewTextBoxColumn.DataPropertyName = "SecondType";
		this.secondTypeDataGridViewTextBoxColumn.HeaderText = "SecondType";
		this.secondTypeDataGridViewTextBoxColumn.Name = "secondTypeDataGridViewTextBoxColumn";
		this.secondTypeDataGridViewTextBoxColumn.ReadOnly = true;
		this.modelIndexDataGridViewTextBoxColumn.DataPropertyName = "ModelIndex";
		this.modelIndexDataGridViewTextBoxColumn.HeaderText = "ModelIndex";
		this.modelIndexDataGridViewTextBoxColumn.Name = "modelIndexDataGridViewTextBoxColumn";
		this.modelIndexDataGridViewTextBoxColumn.ReadOnly = true;
		this.iconIndexDataGridViewTextBoxColumn.DataPropertyName = "IconIndex";
		this.iconIndexDataGridViewTextBoxColumn.HeaderText = "IconIndex";
		this.iconIndexDataGridViewTextBoxColumn.Name = "iconIndexDataGridViewTextBoxColumn";
		this.iconIndexDataGridViewTextBoxColumn.ReadOnly = true;
		this.gradeDataGridViewTextBoxColumn.DataPropertyName = "Grade";
		this.gradeDataGridViewTextBoxColumn.HeaderText = "Grade";
		this.gradeDataGridViewTextBoxColumn.Name = "gradeDataGridViewTextBoxColumn";
		this.gradeDataGridViewTextBoxColumn.ReadOnly = true;
		this.qualityDataGridViewTextBoxColumn.DataPropertyName = "Quality";
		this.qualityDataGridViewTextBoxColumn.HeaderText = "Quality";
		this.qualityDataGridViewTextBoxColumn.Name = "qualityDataGridViewTextBoxColumn";
		this.qualityDataGridViewTextBoxColumn.ReadOnly = true;
		this.quality2DataGridViewTextBoxColumn.DataPropertyName = "Quality2";
		this.quality2DataGridViewTextBoxColumn.HeaderText = "Quality2";
		this.quality2DataGridViewTextBoxColumn.Name = "quality2DataGridViewTextBoxColumn";
		this.quality2DataGridViewTextBoxColumn.ReadOnly = true;
		this.priceDataGridViewTextBoxColumn.DataPropertyName = "Price";
		this.priceDataGridViewTextBoxColumn.HeaderText = "Price";
		this.priceDataGridViewTextBoxColumn.Name = "priceDataGridViewTextBoxColumn";
		this.priceDataGridViewTextBoxColumn.ReadOnly = true;
		this.descriptionDataGridViewTextBoxColumn.DataPropertyName = "Description";
		this.descriptionDataGridViewTextBoxColumn.HeaderText = "Description";
		this.descriptionDataGridViewTextBoxColumn.Name = "descriptionDataGridViewTextBoxColumn";
		this.descriptionDataGridViewTextBoxColumn.ReadOnly = true;
		this.canDropDataGridViewCheckBoxColumn.DataPropertyName = "CanDrop";
		this.canDropDataGridViewCheckBoxColumn.HeaderText = "CanDrop";
		this.canDropDataGridViewCheckBoxColumn.Name = "canDropDataGridViewCheckBoxColumn";
		this.canDropDataGridViewCheckBoxColumn.ReadOnly = true;
		this.canStoreDataGridViewCheckBoxColumn.DataPropertyName = "CanStore";
		this.canStoreDataGridViewCheckBoxColumn.HeaderText = "CanStore";
		this.canStoreDataGridViewCheckBoxColumn.Name = "canStoreDataGridViewCheckBoxColumn";
		this.canStoreDataGridViewCheckBoxColumn.ReadOnly = true;
		this.canNPCDataGridViewCheckBoxColumn.DataPropertyName = "CanNPC";
		this.canNPCDataGridViewCheckBoxColumn.HeaderText = "CanNPC";
		this.canNPCDataGridViewCheckBoxColumn.Name = "canNPCDataGridViewCheckBoxColumn";
		this.canNPCDataGridViewCheckBoxColumn.ReadOnly = true;
		this.canTradeDataGridViewCheckBoxColumn.DataPropertyName = "CanTrade";
		this.canTradeDataGridViewCheckBoxColumn.HeaderText = "CanTrade";
		this.canTradeDataGridViewCheckBoxColumn.Name = "canTradeDataGridViewCheckBoxColumn";
		this.canTradeDataGridViewCheckBoxColumn.ReadOnly = true;
		this.cashCheckDataGridViewTextBoxColumn.DataPropertyName = "CashCheck";
		this.cashCheckDataGridViewTextBoxColumn.HeaderText = "CashCheck";
		this.cashCheckDataGridViewTextBoxColumn.Name = "cashCheckDataGridViewTextBoxColumn";
		this.cashCheckDataGridViewTextBoxColumn.ReadOnly = true;
		this.timeDataGridViewTextBoxColumn.DataPropertyName = "Time";
		this.timeDataGridViewTextBoxColumn.HeaderText = "Time";
		this.timeDataGridViewTextBoxColumn.Name = "timeDataGridViewTextBoxColumn";
		this.timeDataGridViewTextBoxColumn.ReadOnly = true;
		this.applyClanDataGridViewTextBoxColumn.DataPropertyName = "ApplyClan";
		this.applyClanDataGridViewTextBoxColumn.HeaderText = "ApplyClan";
		this.applyClanDataGridViewTextBoxColumn.Name = "applyClanDataGridViewTextBoxColumn";
		this.applyClanDataGridViewTextBoxColumn.ReadOnly = true;
		this.contrib1DataGridViewTextBoxColumn.DataPropertyName = "Contrib1";
		this.contrib1DataGridViewTextBoxColumn.HeaderText = "Contrib1";
		this.contrib1DataGridViewTextBoxColumn.Name = "contrib1DataGridViewTextBoxColumn";
		this.contrib1DataGridViewTextBoxColumn.ReadOnly = true;
		this.contrib2DataGridViewTextBoxColumn.DataPropertyName = "Contrib2";
		this.contrib2DataGridViewTextBoxColumn.HeaderText = "Contrib2";
		this.contrib2DataGridViewTextBoxColumn.Name = "contrib2DataGridViewTextBoxColumn";
		this.contrib2DataGridViewTextBoxColumn.ReadOnly = true;
		this.fameDataGridViewTextBoxColumn.DataPropertyName = "Fame";
		this.fameDataGridViewTextBoxColumn.HeaderText = "Fame";
		this.fameDataGridViewTextBoxColumn.Name = "fameDataGridViewTextBoxColumn";
		this.fameDataGridViewTextBoxColumn.ReadOnly = true;
		this.itemBaseBindingSource.DataSource = typeof(ItemTableReader.ItemBase);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(759, 344);
		base.Controls.Add(this.splitContainer1);
		base.Name = "FormItemRanks";
		this.Text = "FormItemRanks";
		base.Load += new System.EventHandler(FormItemRanks_Load);
		this.splitContainer1.Panel1.ResumeLayout(false);
		this.splitContainer1.Panel2.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.splitContainer1).EndInit();
		this.splitContainer1.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.dataGridView1).EndInit();
		((System.ComponentModel.ISupportInitialize)this.itemBaseBindingSource).EndInit();
		base.ResumeLayout(false);
	}
}
