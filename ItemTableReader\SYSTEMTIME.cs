using System.IO;

namespace ItemTableReader;

public class SYSTEMTIME
{
	internal ushort year;

	internal ushort month;

	internal ushort dayOfWeek;

	internal ushort day;

	internal ushort hour;

	internal ushort minute;

	internal ushort second;

	internal ushort milliseconds;

	public void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		year = binaryReader.ReadUInt16();
		month = binaryReader.ReadUInt16();
		dayOfWeek = binaryReader.ReadUInt16();
		day = binaryReader.ReadUInt16();
		hour = binaryReader.ReadUInt16();
		minute = binaryReader.ReadUInt16();
		second = binaryReader.ReadUInt16();
		milliseconds = binaryReader.ReadUInt16();
	}

	public void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(year);
		binaryWriter.Write(month);
		binaryWriter.Write(dayOfWeek);
		binaryWriter.Write(day);
		binaryWriter.Write(hour);
		binaryWriter.Write(minute);
		binaryWriter.Write(second);
		binaryWriter.Write(milliseconds);
	}

	public string GetDate()
	{
		string text = "";
		text = text + year + "-" + month + "-" + day;
		text += "\t";
		return text + hour + ":" + minute + ":" + second + "." + milliseconds;
	}
}
