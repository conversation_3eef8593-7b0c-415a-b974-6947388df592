using System.IO;

namespace ItemTableReader;

internal class RequiredSkill
{
	private ushort skillID;

	private sbyte skillStep;

	public ushort SkillID
	{
		get
		{
			return skillID;
		}
		set
		{
			skillID = value;
		}
	}

	public sbyte SkillStep
	{
		get
		{
			return skillStep;
		}
		set
		{
			skillStep = value;
		}
	}

	public void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		skillID = binaryReader.ReadUInt16();
		skillStep = binaryReader.ReadSByte();
	}

	public void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(skillID);
		binaryWriter.Write(skillStep);
	}
}
