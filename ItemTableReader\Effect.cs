using System.IO;

namespace ItemTableReader;

internal class Effect
{
	public string Desc;

	public ushort ID { get; set; }

	public ushort Value { get; set; }

	public string Description
	{
		get
		{
			if (ID == 0 || string.IsNullOrWhiteSpace(Desc))
			{
				return "";
			}
			return string.Format(Desc.Replace("%d", "{0}").Replace("%%", "%"), Value);
		}
	}

	public string Name { get; set; }

	public byte Prob { get; set; }

	public byte Type { get; set; }

	public ushort Per { get; set; }

	public Effect()
	{
	}

	public Effect(Effect e)
	{
		MemberwiseClone();
	}

	public void Load(byte[] s)
	{
		ID = (ushort)((s[1] << 8) | s[0]);
		Value = (ushort)((s[3] << 8) | s[2]);
		Prob = s[4];
		Type = s[5];
		Per = (ushort)((s[7] << 8) | s[6]);
		Name = XsdManager.GetInfoFrom("EffectsName", ID);
		Desc = XsdManager.GetInfoFrom("EffectsInfo", ID);
	}

	public void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(ID);
		binaryWriter.Write(Value);
		binaryWriter.Write(Prob);
		binaryWriter.Write(Type);
		binaryWriter.Write(Per);
	}
}
