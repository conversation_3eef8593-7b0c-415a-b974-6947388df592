# Item Ranks 导出功能

## 功能概述

为 Item Ranks 窗口添加了导出信息的表格功能，允许用户将所有物品等级信息导出为 CSV 格式文件。

## 新增功能

### 1. 导出按钮
- 在 FormItemRanks 窗口的左侧面板底部添加了"导出信息"按钮
- 按钮位置：左侧等级列表下方

### 2. CSV 导出功能
- 支持将所有物品等级信息导出为 CSV 格式
- 默认文件名：`ItemRanks_Export.csv`
- 支持用户自定义保存路径和文件名

### 3. 导出的数据字段
导出的 CSV 文件包含以下字段：
- ItemRank: 物品等级
- ID: 物品ID
- Name: 物品名称
- Type: 物品类型
- SecondType: 物品二级类型
- ModelIndex: 模型索引
- IconIndex: 图标索引
- Grade: 等级
- Quality: 品质
- Quality2: 品质2
- Price: 价格
- Description: 描述
- CanDrop: 是否可掉落
- CanStore: 是否可存储
- CanNPC: 是否可NPC交易
- CanTrade: 是否可交易
- CashCheck: 现金检查
- Time: 时间
- ApplyClan: 适用氏族
- Contrib1: 贡献值1
- Contrib2: 贡献值2
- Fame: 声望

## 技术实现

### 1. ItemBase 类增强
在 `ItemBase.cs` 中添加了便利属性：
```csharp
// Convenience properties for data binding
public bool CanDrop => !BlockDrop;
public bool CanStore => !BlockStorage;
public bool CanNPC => !BlockNpcSell;
public bool CanTrade => BlockTrade == 0;
public int Contrib1 => ClanPoint1;
public int Contrib2 => ClanPoint2;
```

### 2. FormItemRanks 类增强
在 `FormItemRanks.cs` 中添加了：
- 导出按钮控件
- 导出事件处理方法
- CSV 文件生成逻辑
- CSV 字符串转义处理

### 3. 主要方法

#### btnExport_Click
处理导出按钮点击事件，显示保存文件对话框

#### ExportToCSV
核心导出方法，生成 CSV 文件内容

#### EscapeCSV
处理 CSV 格式中的特殊字符转义

## 使用方法

1. 启动应用程序
2. 点击主界面的"All Ranks"按钮打开 Item Ranks 窗口
3. 在左侧面板底部点击"导出信息"按钮
4. 在保存文件对话框中选择保存位置和文件名
5. 点击保存完成导出

## 文件格式

导出的 CSV 文件使用 UTF-8 编码，可以在 Excel、LibreOffice Calc 等电子表格软件中打开。

## 注意事项

- 导出功能会处理所有已加载的物品等级数据
- 文件名和描述字段中的引号会被自动转义
- 如果导出过程中出现错误，会显示相应的错误信息
- ItemRank 值使用字典键值确保准确性，避免个别物品ItemRank属性未正确设置的问题

## 修复记录

### v1.1 - ItemRank 值修复
- **问题**: 导出的CSV中某些物品的ItemRank字段可能为空或不正确
- **原因**: 部分物品的ItemRank属性可能未正确设置
- **解决方案**: 修改导出逻辑，使用ItemParser.ItemRanks字典的键值作为ItemRank，确保数据准确性
- **修改代码**:
```csharp
// 修改前
writer.WriteLine($"{item.ItemRank},{item.ID},...");

// 修改后
writer.WriteLine($"{rank},{item.ID},...");
```
