using System.IO;

namespace ItemTableReader;

internal class ItemBoxKey : ItemBase
{
	public static Map<ItemBoxKey> BoxKeys = new Map<ItemBoxKey>();

	public static uint Size => 62u;

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(58));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 58);
		num += 58;
	}
}
