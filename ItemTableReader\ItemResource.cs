using System.IO;

namespace ItemTableReader;

internal class ItemResource : ItemBase
{
	private static uint XSD_START_INDEX = 50000u;

	private uint _xsdName;

	private uint _xsdInfo;

	private static Map<ItemResource> mapResources = new Map<ItemResource>();

	public uint xsdName;

	public uint xsdInfo;

	public short packageNumber;

	public static uint Size => 63u;

	public short Package => packageNumber;

	public static Map<ItemResource> Resources => mapResources;

	public void updateXsdName()
	{
		_xsdName = xsdName + XSD_START_INDEX;
	}

	public void updateXsdInfo()
	{
		_xsdInfo = xsdInfo + XSD_START_INDEX;
	}

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		ItemRank = binaryReader.ReadUInt16();
		base.Grade = binaryReader.ReadByte();
		_xsdInfo = binaryReader.ReadUInt32();
		base.Quality = binaryReader.ReadByte();
		base.Quality2 = binaryReader.ReadByte();
		base.ApplyClan = binaryReader.ReadSByte();
		base.ClanPoint1 = binaryReader.ReadInt32();
		base.ClanPoint2 = binaryReader.ReadInt32();
		base.Price = binaryReader.ReadUInt32();
		unknownBytes.AddRange(binaryReader.ReadBytes(6));
		packageNumber = binaryReader.ReadInt16();
		_xsdName = binaryReader.ReadUInt32();
		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		base.Name = XsdManager.GetInfoFrom("ResourceName", xsdName);
		base.Description = XsdManager.GetInfoFrom("ResourceInfo", xsdInfo);
		base.BlockDrop = binaryReader.ReadBoolean();
		BlockTrade = binaryReader.ReadByte();
		BlockNpcSell = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(7));
		base.CashCheck = binaryReader.ReadSByte();
		base.Time = binaryReader.ReadInt16();
		base.BlockStorage = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(7));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(ItemRank);
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(base.Quality);
		binaryWriter.Write(base.Quality2);
		binaryWriter.Write(base.ApplyClan);
		binaryWriter.Write(base.ClanPoint1);
		binaryWriter.Write(base.ClanPoint2);
		binaryWriter.Write(base.Price);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 6);
		num += 6;
		binaryWriter.Write(packageNumber);
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(base.BlockDrop);
		binaryWriter.Write(BlockTrade);
		binaryWriter.Write(BlockNpcSell);
		binaryWriter.Write(unknownBytes.ToArray(), num, 7);
		num += 7;
		binaryWriter.Write(base.CashCheck);
		binaryWriter.Write(base.Time);
		binaryWriter.Write(base.BlockStorage);
		binaryWriter.Write(unknownBytes.ToArray(), num, 7);
		num += 7;
	}
}
