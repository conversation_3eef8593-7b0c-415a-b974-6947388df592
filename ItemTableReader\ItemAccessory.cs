using System.IO;

namespace ItemTableReader;

internal class ItemAccessory : ItemBase
{
	private static uint XSD_START_INDEX = 45000u;

	private uint _xsdName;

	private uint _xsdInfo;

	private static Map<ItemAccessory> mapAccessories = new Map<ItemAccessory>();

	public short group;

	public short itemCase;

	public uint xsdName;

	public uint xsdInfo;

	public sbyte pack;

	public sbyte clan;

	public sbyte rank;

	public ushort durability;

	public Effect[] effects = new Effect[5];

	public Special[] specials = new Special[5];

	public short requiredLevel;

	public short requiredDex;

	public short requiredStr;

	public short requiredCon;

	public short requiredEss;

	public short requiredWis;

	public byte maxSocket;

	public short itemSet;

	public static uint Size => 155u;

	public static Map<ItemAccessory> Accessories => mapAccessories;

	public short RequiredLevel => requiredLevel;

	public short RequiredDex => requiredDex;

	public short RequiredStr => requiredStr;

	public short RequiredCon => requiredCon;

	public short RequiredEss => requiredEss;

	public short RequiredWis => requiredWis;

	public byte MaxSockets => maxSocket;

	public short ItemSet => itemSet;

	public Effect[] Effects => effects;

	public Special[] Specials => specials;

	public short Case => itemCase;

	public short Group => group;

	public void updateXsdName()
	{
		_xsdName = xsdName + XSD_START_INDEX;
	}

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		ItemRank = binaryReader.ReadUInt16();
		group = binaryReader.ReadInt16();
		itemCase = binaryReader.ReadInt16();
		base.Grade = binaryReader.ReadByte();
		_xsdInfo = binaryReader.ReadUInt32();
		_xsdName = binaryReader.ReadUInt32();
		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		int key = (int)xsdName;
		if (XsdManager.Maps["AccessoryName"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["AccessoryName"], XsdManager.TableNames["AccessoryName"]);
		}
		if (XsdManager.Maps["AccessoryInfo"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["AccessoryInfo"], XsdManager.TableNames["AccessoryInfo"]);
		}
		if (XsdManager.Maps["AccessoryName"].ContainsKey(key))
		{
			base.Name = XsdManager.Maps["AccessoryName"][key];
		}
		key = (int)xsdInfo;
		if (XsdManager.Maps["AccessoryInfo"].ContainsKey(key))
		{
			base.Description = XsdManager.Maps["AccessoryInfo"][key];
		}
		pack = binaryReader.ReadSByte();
		clan = binaryReader.ReadSByte();
		rank = binaryReader.ReadSByte();
		durability = binaryReader.ReadUInt16();
		base.Quality = binaryReader.ReadByte();
		base.Quality2 = binaryReader.ReadByte();
		if (XsdManager.EffectsName.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.EffectsName, "CharacterState_name");
		}
		if (XsdManager.EffectsInfo.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.EffectsInfo, "CharacterState_info");
		}
		for (int i = 0; i < 5; i++)
		{
			effects[i] = new Effect();
			effects[i].Load(binaryReader.ReadBytes(8));
			if (XsdManager.EffectsInfo.ContainsKey(effects[i].ID))
			{
				effects[i].Desc = XsdManager.EffectsInfo[effects[i].ID];
			}
			if (XsdManager.EffectsName.ContainsKey(effects[i].ID))
			{
				effects[i].Name = XsdManager.EffectsName[effects[i].ID];
			}
		}
		requiredLevel = binaryReader.ReadInt16();
		requiredDex = binaryReader.ReadInt16();
		requiredStr = binaryReader.ReadInt16();
		requiredCon = binaryReader.ReadInt16();
		requiredEss = binaryReader.ReadInt16();
		requiredWis = binaryReader.ReadInt16();
		base.ApplyClan = binaryReader.ReadSByte();
		base.ClanPoint1 = binaryReader.ReadInt32();
		base.ClanPoint2 = binaryReader.ReadInt32();
		base.Price = binaryReader.ReadUInt32();
		for (int j = 0; j < 4; j++)
		{
			specials[j] = new Special();
			specials[j].Load(binaryReader.ReadBytes(8));
		}
		unknownBytes.AddRange(binaryReader.ReadBytes(8));
		maxSocket = binaryReader.ReadByte();
		itemSet = binaryReader.ReadInt16();
		base.BlockDrop = binaryReader.ReadBoolean();
		BlockTrade = binaryReader.ReadByte();
		BlockNpcSell = binaryReader.ReadBoolean();
		base.Fame = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(6));
		base.CashCheck = binaryReader.ReadSByte();
		base.Time = binaryReader.ReadInt16();
		base.BlockStorage = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(3));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(ItemRank);
		binaryWriter.Write(group);
		binaryWriter.Write(itemCase);
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(pack);
		binaryWriter.Write(clan);
		binaryWriter.Write(rank);
		binaryWriter.Write(durability);
		binaryWriter.Write(base.Quality);
		binaryWriter.Write(base.Quality2);
		for (int i = 0; i < 5; i++)
		{
			effects[i].Save(s);
		}
		binaryWriter.Write(requiredLevel);
		binaryWriter.Write(requiredDex);
		binaryWriter.Write(requiredStr);
		binaryWriter.Write(requiredCon);
		binaryWriter.Write(requiredEss);
		binaryWriter.Write(requiredWis);
		binaryWriter.Write(base.ApplyClan);
		binaryWriter.Write(base.ClanPoint1);
		binaryWriter.Write(base.ClanPoint2);
		binaryWriter.Write(base.Price);
		for (int j = 0; j < 4; j++)
		{
			specials[j].Save(s);
		}
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 8);
		num += 8;
		binaryWriter.Write(maxSocket);
		binaryWriter.Write(itemSet);
		binaryWriter.Write(base.BlockDrop);
		binaryWriter.Write(BlockTrade);
		binaryWriter.Write(BlockNpcSell);
		binaryWriter.Write(base.Fame);
		binaryWriter.Write(unknownBytes.ToArray(), num, 6);
		num += 6;
		binaryWriter.Write(base.CashCheck);
		binaryWriter.Write(base.Time);
		binaryWriter.Write(base.BlockStorage);
		binaryWriter.Write(unknownBytes.ToArray(), num, 3);
		num += 3;
	}
}
