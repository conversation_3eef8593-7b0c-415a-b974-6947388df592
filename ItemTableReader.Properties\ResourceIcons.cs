using System;
using System.CodeDom.Compiler;
using System.Collections;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ItemTableReader.Properties
{
    [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [DebuggerNonUserCode]
    [CompilerGenerated]
    internal class ResourceIcons
    {
        private static ResourceManager resourceMan;
        private static CultureInfo resourceCulture;

        [EditorBrowsable(EditorBrowsableState.Advanced)]
        internal static ResourceManager ResourceManager
        {
            get
            {
                if (ResourceIcons.resourceMan == null)
                {
                    ResourceManager temp = new ResourceManager("ItemTableReader.Properties.ResourceIcons", typeof(ResourceIcons).Assembly);
                    ResourceIcons.resourceMan = temp;
                }
                return ResourceIcons.resourceMan;
            }
        }

        [EditorBrowsable(EditorBrowsableState.Advanced)]
        internal static CultureInfo Culture
        {
            get
            {
                return ResourceIcons.resourceCulture;
            }
            set
            {
                ResourceIcons.resourceCulture = value;
            }
        }

        internal static Bitmap RESOURCE1
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE1", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE2
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE2", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE3
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE3", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE4
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE4", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE5
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE5", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE6
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE6", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE7
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE7", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE8
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE8", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE9
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE9", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE10
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE10", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE11
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE11", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE12
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE12", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE13
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE13", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE14
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE14", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE15
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE15", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE16
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE16", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE17
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE17", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE18
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE18", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE19
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE19", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE20
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE20", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE21
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE21", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE22
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE22", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE23
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE23", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE24
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE24", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE25
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE25", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE26
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE26", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE27
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE27", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE28
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE28", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE29
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE29", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE30
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE30", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE31
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE31", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE32
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE32", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE33
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE33", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE34
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE34", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE35
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE35", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE36
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE36", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE37
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE37", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE38
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE38", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE39
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE39", resourceCulture);
            }
        }

        internal static Bitmap RESOURCE40
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("RESOURCE40", resourceCulture);
            }
        }

        /// <summary>
        /// 获取指定索引的资源图标
        /// </summary>
        /// <param name="index">资源索引，从1开始</param>
        /// <returns>资源图标位图，如果不存在则返回null</returns>
        internal static Bitmap GetResourceBitmap(int index)
        {
            string resourceName = $"RESOURCE{index}";
            try
            {
                return (Bitmap)ResourceManager.GetObject(resourceName, resourceCulture);
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"Resource not found: {resourceName}");
                return null;
            }
        }

        internal ResourceIcons()
        {
        }

        public static void DebugAvailableResources()
        {
            ResourceManager rm = ResourceManager;
            ResourceSet rs = rm.GetResourceSet(CultureInfo.CurrentUICulture, true, true);
            if (rs != null)
            {
                Console.WriteLine("ResourceIcons Available Resources:");
                foreach (DictionaryEntry entry in rs)
                {
                    string resourceKey = entry.Key.ToString();
                    object resource = entry.Value;
                    Console.WriteLine($"Resource Key: {resourceKey}, Type: {resource?.GetType().Name ?? "null"}");
                    Debug.WriteLine($"Resource Key: {resourceKey}, Type: {resource?.GetType().Name ?? "null"}");
                }
            }
            else
            {
                Console.WriteLine("No resources found in ResourceIcons.");
                Debug.WriteLine("No resources found in ResourceIcons.");
            }
        }
    }
}
