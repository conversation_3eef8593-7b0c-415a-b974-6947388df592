using System;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormLifes : Form
{
	private static ItemLife tempLife = new ItemLife();

	private IContainer components;

	private SplitContainer splitContainer1;

	private ListBox listBoxLifes;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPageUnknown;

	private ByteViewer byteViewer = new ByteViewer();

	private TableLayoutPanel tableLayoutPanel1;

	private Label label1;

	private Label label2;

	private Label label3;

	private Label label4;

	private TextBox textBoxID;

	private TextBox textBoxType;

	private TextBox textBox2ndType;

	private TextBox textBoxRank;

	private Label labelDescription;

	private Button btnCopyItem;

	private Button btnPasteItem;

	private Button btnIcon;

	public FormLifes()
	{
		InitializeComponent();
	}

	private void FormLifes_Load(object sender, EventArgs e)
	{
		listBoxLifes.BeginUpdate();
		foreach (ItemLife value in ItemLife.Lifes.Values)
		{
			listBoxLifes.Items.Add(value);
		}
		listBoxLifes.EndUpdate();
		listBoxLifes.DisplayMember = "FullName";
		listBoxLifes.ValueMember = "ID";
		tabPageUnknown.Controls.Add(byteViewer);
		byteViewer.Dock = DockStyle.Fill;
	}

	private void listBoxLifes_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (listBoxLifes.SelectedItem is ItemLife itemLife)
		{
			byteViewer.SetBytes(itemLife.unknownBytes.ToArray());
			textBoxType.TextChanged -= textBoxType_TextChanged;
			textBox2ndType.TextChanged -= textBox2ndType_TextChanged;
			textBoxRank.TextChanged -= textBoxRank_TextChanged;
			textBoxID.Text = itemLife.ID.ToString();
			textBoxRank.Text = itemLife.ItemRank.ToString();
			textBoxType.Text = itemLife.Type.ToString();
			textBox2ndType.Text = itemLife.SecondType.ToString();
			textBoxType.TextChanged += textBoxType_TextChanged;
			textBox2ndType.TextChanged += textBox2ndType_TextChanged;
			textBoxRank.TextChanged += textBoxRank_TextChanged;
			labelDescription.Text = itemLife.Description;

			// 更新图标显示
			try
			{
				Bitmap icon = ItemParser.SetIcon(itemLife);
				if (icon != null)
				{
					btnIcon.Image = icon;
					btnIcon.ImageAlign = ContentAlignment.MiddleCenter;
					btnIcon.TextAlign = ContentAlignment.BottomCenter;
				}
			}
			catch (Exception ex)
			{
				System.Diagnostics.Debug.WriteLine($"Error setting icon: {ex.Message}");
			}
		}
	}

	private void splitContainer1_KeyDown(object sender, KeyEventArgs e)
	{
		FormLifes_KeyDown(sender, e);
		e.SuppressKeyPress = true;
		splitContainer1.IsSplitterFixed = true;
	}

	private void splitContainer1_KeyUp(object sender, KeyEventArgs e)
	{
		splitContainer1.IsSplitterFixed = false;
	}

	private void FormLifes_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Up)
		{
			if (listBoxLifes.SelectedIndex > 0)
			{
				listBoxLifes.SelectedIndex--;
			}
		}
		else if (e.KeyCode == Keys.Down && listBoxLifes.SelectedIndex < listBoxLifes.Items.Count - 1)
		{
			listBoxLifes.SelectedIndex++;
		}
	}

	private void textBoxType_TextChanged(object sender, EventArgs e)
	{
		if (listBoxLifes.SelectedItem is ItemLife itemLife && sbyte.TryParse(textBoxType.Text, out var result))
		{
			itemLife.Type = result;
		}
	}

	private void textBox2ndType_TextChanged(object sender, EventArgs e)
	{
		if (listBoxLifes.SelectedItem is ItemLife itemLife && sbyte.TryParse(textBox2ndType.Text, out var result))
		{
			itemLife.SecondType = result;
		}
	}

	private void textBoxRank_TextChanged(object sender, EventArgs e)
	{
		if (listBoxLifes.SelectedItem is ItemLife itemLife && ushort.TryParse(textBoxRank.Text, out var result))
		{
			itemLife.ItemRank = result;
		}
	}

	private void btnCopyItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (!(listBoxLifes.SelectedItem is ItemLife itemLife))
			{
				return;
			}
			tempLife.Type = itemLife.Type;
			tempLife.SecondType = itemLife.SecondType;
			tempLife._xsdInfo = itemLife._xsdInfo;
			if (tempLife.unknownBytes.Count == 0)
			{
				byte item = 0;
				for (int i = 0; i < itemLife.unknownBytes.Count; i++)
				{
					tempLife.unknownBytes.Add(item);
				}
			}
			for (int j = 0; j < tempLife.unknownBytes.Count; j++)
			{
				tempLife.unknownBytes[j] = itemLife.unknownBytes[j];
			}
			btnPasteItem.Enabled = true;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btnPasteItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxLifes.SelectedItem is ItemLife itemLife)
			{
				itemLife.Type = tempLife.Type;
				itemLife.SecondType = tempLife.SecondType;
				itemLife._xsdInfo = tempLife._xsdInfo;
				for (int i = 0; i < tempLife.unknownBytes.Count; i++)
				{
					itemLife.unknownBytes[i] = tempLife.unknownBytes[i];
				}
				itemLife.ItemRank = (ushort)(itemLife.unknownBytes[31] + itemLife.unknownBytes[32] * 256);
			}
			listBoxLifes_SelectedIndexChanged(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.splitContainer1 = new System.Windows.Forms.SplitContainer();
		this.listBoxLifes = new System.Windows.Forms.ListBox();
		this.tabControl1 = new System.Windows.Forms.TabControl();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
		this.label1 = new System.Windows.Forms.Label();
		this.label2 = new System.Windows.Forms.Label();
		this.label3 = new System.Windows.Forms.Label();
		this.label4 = new System.Windows.Forms.Label();
		this.textBoxID = new System.Windows.Forms.TextBox();
		this.textBoxType = new System.Windows.Forms.TextBox();
		this.textBox2ndType = new System.Windows.Forms.TextBox();
		this.textBoxRank = new System.Windows.Forms.TextBox();
		this.labelDescription = new System.Windows.Forms.Label();
		this.tabPageUnknown = new System.Windows.Forms.TabPage();
		this.btnCopyItem = new System.Windows.Forms.Button();
		this.btnPasteItem = new System.Windows.Forms.Button();
		this.btnIcon = new System.Windows.Forms.Button();
		((System.ComponentModel.ISupportInitialize)this.splitContainer1).BeginInit();
		this.splitContainer1.Panel1.SuspendLayout();
		this.splitContainer1.Panel2.SuspendLayout();
		this.splitContainer1.SuspendLayout();
		this.tabControl1.SuspendLayout();
		this.tabPage1.SuspendLayout();
		this.tableLayoutPanel1.SuspendLayout();
		base.SuspendLayout();
		this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.splitContainer1.Location = new System.Drawing.Point(0, 0);
		this.splitContainer1.Name = "splitContainer1";
		this.splitContainer1.Panel1.Controls.Add(this.listBoxLifes);
		this.splitContainer1.Panel2.Controls.Add(this.tabControl1);
		this.splitContainer1.Size = new System.Drawing.Size(526, 385);
		this.splitContainer1.SplitterDistance = 175;
		this.splitContainer1.TabIndex = 0;
		this.splitContainer1.KeyDown += new System.Windows.Forms.KeyEventHandler(splitContainer1_KeyDown);
		this.splitContainer1.KeyUp += new System.Windows.Forms.KeyEventHandler(splitContainer1_KeyUp);
		this.listBoxLifes.Dock = System.Windows.Forms.DockStyle.Fill;
		this.listBoxLifes.FormattingEnabled = true;
		this.listBoxLifes.Location = new System.Drawing.Point(0, 0);
		this.listBoxLifes.Name = "listBoxLifes";
		this.listBoxLifes.Size = new System.Drawing.Size(175, 385);
		this.listBoxLifes.TabIndex = 0;
		this.listBoxLifes.SelectedIndexChanged += new System.EventHandler(listBoxLifes_SelectedIndexChanged);
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPageUnknown);
		this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tabControl1.Location = new System.Drawing.Point(0, 0);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new System.Drawing.Size(347, 385);
		this.tabControl1.TabIndex = 0;
		this.tabPage1.Controls.Add(this.tableLayoutPanel1);
		this.tabPage1.Location = new System.Drawing.Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(339, 359);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "tabPage1";
		this.tabPage1.UseVisualStyleBackColor = true;
		this.tableLayoutPanel1.ColumnCount = 2;
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.Controls.Add(this.label1, 0, 0);
		this.tableLayoutPanel1.Controls.Add(this.label2, 0, 1);
		this.tableLayoutPanel1.Controls.Add(this.label3, 0, 2);
		this.tableLayoutPanel1.Controls.Add(this.label4, 0, 3);
		this.tableLayoutPanel1.Controls.Add(this.textBoxID, 1, 0);
		this.tableLayoutPanel1.Controls.Add(this.textBoxType, 1, 1);
		this.tableLayoutPanel1.Controls.Add(this.textBox2ndType, 1, 2);
		this.tableLayoutPanel1.Controls.Add(this.textBoxRank, 1, 3);
		this.tableLayoutPanel1.Controls.Add(this.labelDescription, 0, 4);
		this.tableLayoutPanel1.Controls.Add(this.btnIcon, 0, 5);
		this.tableLayoutPanel1.Controls.Add(this.btnCopyItem, 1, 5);
		this.tableLayoutPanel1.Controls.Add(this.btnPasteItem, 1, 6);
		this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel1.Name = "tableLayoutPanel1";
		this.tableLayoutPanel1.RowCount = 9;
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 36f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8f));
		this.tableLayoutPanel1.Size = new System.Drawing.Size(333, 317);
		this.tableLayoutPanel1.TabIndex = 0;
		this.label1.Location = new System.Drawing.Point(3, 0);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(18, 26);
		this.label1.TabIndex = 0;
		this.label1.Text = "ID";
		this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label2.Location = new System.Drawing.Point(3, 26);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(100, 23);
		this.label2.TabIndex = 1;
		this.label2.Text = "Type";
		this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label3.Location = new System.Drawing.Point(3, 52);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(100, 23);
		this.label3.TabIndex = 2;
		this.label3.Text = "Second type";
		this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label4.Location = new System.Drawing.Point(3, 78);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(100, 23);
		this.label4.TabIndex = 3;
		this.label4.Text = "Rank";
		this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxID.Enabled = false;
		this.textBoxID.Location = new System.Drawing.Point(109, 3);
		this.textBoxID.Name = "textBoxID";
		this.textBoxID.Size = new System.Drawing.Size(100, 20);
		this.textBoxID.TabIndex = 4;
		this.textBoxType.Location = new System.Drawing.Point(109, 29);
		this.textBoxType.Name = "textBoxType";
		this.textBoxType.Size = new System.Drawing.Size(100, 20);
		this.textBoxType.TabIndex = 5;
		this.textBoxType.TextChanged += new System.EventHandler(textBoxType_TextChanged);
		this.textBox2ndType.Location = new System.Drawing.Point(109, 55);
		this.textBox2ndType.Name = "textBox2ndType";
		this.textBox2ndType.Size = new System.Drawing.Size(100, 20);
		this.textBox2ndType.TabIndex = 6;
		this.textBox2ndType.TextChanged += new System.EventHandler(textBox2ndType_TextChanged);
		this.textBoxRank.Location = new System.Drawing.Point(109, 81);
		this.textBoxRank.Name = "textBoxRank";
		this.textBoxRank.Size = new System.Drawing.Size(100, 20);
		this.textBoxRank.TabIndex = 7;
		this.textBoxRank.TextChanged += new System.EventHandler(textBoxRank_TextChanged);
		this.labelDescription.BackColor = System.Drawing.SystemColors.ControlLight;
		this.labelDescription.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
		this.tableLayoutPanel1.SetColumnSpan(this.labelDescription, 2);
		this.labelDescription.Location = new System.Drawing.Point(3, 104);
		this.labelDescription.Name = "labelDescription";
		this.labelDescription.Size = new System.Drawing.Size(327, 91);
		this.labelDescription.TabIndex = 8;
		this.tabPageUnknown.Location = new System.Drawing.Point(4, 22);
		this.tabPageUnknown.Name = "tabPageUnknown";
		this.tabPageUnknown.Padding = new System.Windows.Forms.Padding(3);
		this.tabPageUnknown.Size = new System.Drawing.Size(339, 323);
		this.tabPageUnknown.TabIndex = 1;
		this.tabPageUnknown.Text = "tabPage2";
		this.tabPageUnknown.UseVisualStyleBackColor = true;
		this.btnCopyItem.Location = new System.Drawing.Point(109, 198);
		this.btnCopyItem.Name = "btnCopyItem";
		this.btnCopyItem.Size = new System.Drawing.Size(75, 23);
		this.btnCopyItem.TabIndex = 11;
		this.btnCopyItem.Text = "CopyItem";
		this.btnCopyItem.UseVisualStyleBackColor = true;
		this.btnCopyItem.Click += new System.EventHandler(btnCopyItem_Click);
		this.btnPasteItem.Enabled = false;
		this.btnPasteItem.Location = new System.Drawing.Point(109, 233);
		this.btnPasteItem.Name = "btnPasteItem";
		this.btnPasteItem.Size = new System.Drawing.Size(75, 23);
		this.btnPasteItem.TabIndex = 12;
		this.btnPasteItem.Text = "PasteItem";
		this.btnPasteItem.UseVisualStyleBackColor = true;
		this.btnPasteItem.Click += new System.EventHandler(btnPasteItem_Click);
		this.btnIcon.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.btnIcon.Location = new System.Drawing.Point(3, 198);
		this.btnIcon.Name = "btnIcon";
		this.btnIcon.Size = new System.Drawing.Size(32, 30);
		this.btnIcon.TabIndex = 13;
		this.btnIcon.UseVisualStyleBackColor = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(526, 385);
		base.Controls.Add(this.splitContainer1);
		base.Name = "FormLifes";
		this.Text = "FormLifes";
		base.Load += new System.EventHandler(FormLifes_Load);
		base.KeyDown += new System.Windows.Forms.KeyEventHandler(FormLifes_KeyDown);
		this.splitContainer1.Panel1.ResumeLayout(false);
		this.splitContainer1.Panel2.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.splitContainer1).EndInit();
		this.splitContainer1.ResumeLayout(false);
		this.tabControl1.ResumeLayout(false);
		this.tabPage1.ResumeLayout(false);
		this.tableLayoutPanel1.ResumeLayout(false);
		this.tableLayoutPanel1.PerformLayout();
		base.ResumeLayout(false);
	}
}
