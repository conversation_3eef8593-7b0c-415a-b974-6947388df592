using System;
using System.IO;

namespace ItemTableReader;

internal class ItemCloth : ItemBase
{
	private static uint XSD_START_INDEX = 10000u;

	private uint _xsdName;

	private uint _xsdInfo;

	private byte cDump;

	public byte cUserTrade;

	private byte cNPCTrade;

	private static Map<ItemCloth> mapClothes = new Map<ItemCloth>();

	private static Map<ItemCloth> mapClothes2 = new Map<ItemCloth>();

	private static Map<ItemCloth> mapClothes3 = new Map<ItemCloth>();

	public byte slots;

	public byte maxSlots;

	public byte pockets;

	public uint xsdName;

	public uint xsdInfo;

	public short group;

	public byte level;

	public short itemCase;

	public sbyte precedence;

	public sbyte clan;

	public byte characGrade;

	public byte sex;

	public ushort durability;

	public Effect[] effects = new Effect[10];

	public Special[] specials = new Special[4];

	public VectorPos center = new VectorPos();

	public ushort itemSet;

	public ushort def;

	public ushort time;

	public ushort xsdNick;

	private string nickname;

	public static uint Size => 185u;

	public Effect[] Effects => effects;

	public static Map<ItemCloth> Clothes => mapClothes;

	public static Map<ItemCloth> Clothes2 => mapClothes2;

	public ushort Defence => def;

	public static Map<ItemCloth> Clothes3 => mapClothes3;

	public void updateXsdName()
	{
		_xsdName = xsdName + XSD_START_INDEX;
	}

	public void updateXsdInfo()
	{
		_xsdInfo = xsdInfo + XSD_START_INDEX;
	}

	public override void Load(Stream s)
	{
		if (XsdManager.Nicknames.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Nicknames, "ItemTable_Nickname");
		}
		if (XsdManager.Maps["ClothName"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["ClothName"], XsdManager.TableNames["ClothName"]);
		}
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		if (base.ID == 13961)
		{
			base.SecondType = base.SecondType;
		}
		else if (base.ID == 13965)
		{
			base.SecondType = base.SecondType;
		}
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		ItemRank = binaryReader.ReadUInt16();
		group = binaryReader.ReadInt16();
		itemCase = binaryReader.ReadInt16();
		base.Grade = binaryReader.ReadByte();
		_xsdInfo = binaryReader.ReadUInt32();
		base.ApplyClan = binaryReader.ReadSByte();
		base.ClanPoint1 = binaryReader.ReadInt32();
		base.ClanPoint2 = binaryReader.ReadInt32();
		base.Price = binaryReader.ReadUInt32();
		unknownBytes.AddRange(binaryReader.ReadBytes(4));
		xsdNick = binaryReader.ReadUInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		_xsdName = binaryReader.ReadUInt32();
		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		base.Name = XsdManager.GetInfoFrom("ClothName", xsdName);
		base.Description = XsdManager.GetInfoFrom("ClothInfo", xsdInfo);
		if (XsdManager.Nicknames.ContainsKey(xsdNick))
		{
			nickname = XsdManager.Nicknames[xsdNick];
		}
		precedence = binaryReader.ReadSByte();
		clan = binaryReader.ReadSByte();
		characGrade = binaryReader.ReadByte();
		sex = binaryReader.ReadByte();
		itemSet = binaryReader.ReadUInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(20));
		def = binaryReader.ReadUInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(7));
		durability = binaryReader.ReadUInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(1));
		for (int i = 0; i < 5; i++)
		{
			effects[i] = new Effect();
			effects[i].Load(binaryReader.ReadBytes(8));
		}
		level = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(5));
		pockets = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(32));
		slots = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		cDump = binaryReader.ReadByte();
		base.BlockDrop = Convert.ToBoolean(cDump);
		cUserTrade = binaryReader.ReadByte();
		BlockTrade = cUserTrade;
		cNPCTrade = binaryReader.ReadByte();
		BlockNpcSell = Convert.ToBoolean(cNPCTrade);
		unknownBytes.AddRange(binaryReader.ReadBytes(8));
		time = binaryReader.ReadUInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(5));
		maxSlots = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(ItemRank);
		binaryWriter.Write(group);
		binaryWriter.Write(itemCase);
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(base.ApplyClan);
		binaryWriter.Write(base.ClanPoint1);
		binaryWriter.Write(base.ClanPoint2);
		binaryWriter.Write(base.Price);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 4);
		num += 4;
		binaryWriter.Write(xsdNick);
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(precedence);
		binaryWriter.Write(clan);
		binaryWriter.Write(characGrade);
		binaryWriter.Write(sex);
		binaryWriter.Write(itemSet);
		binaryWriter.Write(unknownBytes.ToArray(), num, 20);
		num += 20;
		binaryWriter.Write(def);
		binaryWriter.Write(unknownBytes.ToArray(), num, 7);
		num += 7;
		binaryWriter.Write(durability);
		binaryWriter.Write(unknownBytes.ToArray(), num, 1);
		num++;
		for (int i = 0; i < 5; i++)
		{
			effects[i].Save(s);
		}
		binaryWriter.Write(level);
		binaryWriter.Write(unknownBytes.ToArray(), num, 5);
		num += 5;
		binaryWriter.Write(pockets);
		binaryWriter.Write(unknownBytes.ToArray(), num, 32);
		num += 32;
		binaryWriter.Write(slots);
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(base.BlockDrop);
		binaryWriter.Write(cUserTrade);
		binaryWriter.Write(BlockNpcSell);
		binaryWriter.Write(unknownBytes.ToArray(), num, 8);
		num += 8;
		binaryWriter.Write(time);
		binaryWriter.Write(unknownBytes.ToArray(), num, 5);
		num += 5;
		binaryWriter.Write(maxSlots);
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
	}
}
