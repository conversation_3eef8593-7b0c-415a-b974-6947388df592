<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <!-- 上衣(UPPER)图标 -->
  <data name="Item_Clo_00a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_00a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_01a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_01a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_02a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_02a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_03a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_03a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_04a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_04a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_05a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_05a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_06a" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_06a.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <!-- 裤子(LOWER)图标 -->
  <data name="Item_Clo_00b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_00b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_01b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_01b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_02b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_02b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_03b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_03b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_04b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_04b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_05b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_05b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_06b" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_06b.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <!-- 手套(GLOVES)图标 -->
  <data name="Item_Clo_00c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_00c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_01c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_01c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_02c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_02c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_03c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_03c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_04c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_04c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_05c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_05c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_06c" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_06c.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <!-- 鞋子(SHOES)图标 -->
  <data name="Item_Clo_00d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_00d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_01d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_01d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_02d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_02d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_03d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_03d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_04d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_04d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_05d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_05d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_06d" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_06d.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <!-- 帽子(CAP)图标 -->
  <data name="Item_Clo_00e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_00e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_01e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_01e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_02e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_02e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_03e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_03e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_04e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_04e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_05e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_05e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_06e" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_06e.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <!-- 背包(BACKPACK)图标 -->
  <data name="Item_Clo_00h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_00h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_01h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_01h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_02h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_02h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_03h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_03h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_04h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_04h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_05h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_05h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Item_Clo_06h" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Item_Clo_06h.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>
