using System.IO;

namespace ItemTableReader;

internal class ItemCounteract : ItemBase
{
	public static Map<ItemCounteract> Counteracts = new Map<ItemCounteract>();

	public static uint Size => 51u;

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(47));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 47);
		num += 47;
	}
}
