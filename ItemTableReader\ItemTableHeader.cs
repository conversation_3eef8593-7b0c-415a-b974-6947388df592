using System;
using System.IO;
using System.Linq;
using System.Text;

namespace ItemTableReader;

internal class ItemTableHeader
{
	private static readonly short MAX_ITEM_COUNT = 23;

	private short[] MaxItemCount = new short[MAX_ITEM_COUNT + 1];

	private byte[] toolName = new byte[48];

	private short version1;

	private short version2;

	private SYSTEMTIME updateTime = new SYSTEMTIME();

	private byte[] userName = new byte[16];

	private byte[] comName = new byte[20];

	public string ToolName => Encoding.Default.GetString(toolName, 0, 48);

	public string UserName => Convert.ToString(userName);

	public string CompanyName => Convert.ToString(comName);

	public SYSTEMTIME Datetime => updateTime;

	public void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		toolName = binaryReader.ReadBytes(48);
		version1 = binaryReader.ReadInt16();
		version2 = binaryReader.ReadInt16();
		updateTime.Load(s);
		userName = binaryReader.ReadBytes(16);
		comName = binaryReader.ReadBytes(20);
		_ = BOOL.TRUE;
		int num = 0;
		int num2 = 0;
		string text = "";
		do
		{
			num = 0;
			sbyte b = binaryReader.ReadSByte();
			uint num3 = binaryReader.ReadUInt32();
			short num4 = binaryReader.ReadInt16();
			short num5 = binaryReader.ReadInt16();
			MaxItemCount[b] = num5;
			ItemType itemType = (ItemType)b;
			byte[] array = null;
			text += $"{itemType}: \t{num3}\n";
			switch (itemType)
			{
			case ItemType.WEAPON:
			case ItemType.WEAPON2:
			case ItemType.WEAPON3:
			{
				if (num4 == 0)
				{
					continue;
				}
				array = binaryReader.ReadBytes((int)num3 * (int)num4);
				using (MemoryStream s3 = new MemoryStream(array))
				{
					while (num++ < num4)
					{
						ItemWeapon itemWeapon = new ItemWeapon();
						itemWeapon.Load(s3);
						if (itemType == ItemType.WEAPON)
						{
							ItemWeapon.Weapons.Add(itemWeapon.ID, itemWeapon);
						}
						if (itemType == ItemType.WEAPON2)
						{
							ItemWeapon.Weapons2.Add(itemWeapon.ID, itemWeapon);
						}
						if (itemType == ItemType.WEAPON3)
						{
							ItemWeapon.Weapons3.Add(itemWeapon.ID, itemWeapon);
						}
						num2++;
					}
				}
				continue;
			}
			case ItemType.CLOTHES:
			case ItemType.CLOTHES2:
			case ItemType.CLOTHES3:
			{
				if (num4 == 0)
				{
					continue;
				}
				array = binaryReader.ReadBytes((int)num3 * (int)num4);
				using (MemoryStream s2 = new MemoryStream(array))
				{
					while (num++ < num4)
					{
						ItemCloth itemCloth = new ItemCloth();
						itemCloth.Load(s2);
						if (itemType == ItemType.CLOTHES)
						{
							ItemCloth.Clothes.Add(itemCloth.ID, itemCloth);
						}
						if (itemType == ItemType.CLOTHES2)
						{
							ItemCloth.Clothes2.Add(itemCloth.ID, itemCloth);
						}
						if (itemType == ItemType.CLOTHES3)
						{
							ItemCloth.Clothes3.Add(itemCloth.ID, itemCloth);
						}
						itemCloth.AddToItemRankMap();
						num2++;
					}
				}
				continue;
			}
			case ItemType.BOOK:
				while (num++ < num4)
				{
					ItemBook itemBook = new ItemBook();
					array = binaryReader.ReadBytes(108);
					itemBook.Load(new MemoryStream(array));
					ItemBook.Books.Add(itemBook.ID, itemBook);
					itemBook.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.POTION:
				while (num++ < num4)
				{
					ItemPotion itemPotion = new ItemPotion();
					array = binaryReader.ReadBytes((int)num3);
					itemPotion.Load(new MemoryStream(array));
					ItemPotion.Potions.Add(itemPotion.ID, itemPotion);
					itemPotion.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.CONSUMING:
				while (num++ < num4)
				{
					ItemConsuming itemConsuming = new ItemConsuming();
					array = binaryReader.ReadBytes((int)num3);
					itemConsuming.Load(new MemoryStream(array));
					ItemConsuming.Consumings.Add(itemConsuming.ID, itemConsuming);
					itemConsuming.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.ELIXIR:
				while (num++ < num4)
				{
					ItemElixir itemElixir = new ItemElixir();
					array = binaryReader.ReadBytes((int)num3);
					itemElixir.Load(new MemoryStream(array));
					ItemElixir.Elixirs.Add(itemElixir.ID, itemElixir);
					itemElixir.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.COUNTERACT:
				while (num++ < num4)
				{
					ItemCounteract itemCounteract = new ItemCounteract();
					array = binaryReader.ReadBytes((int)num3);
					itemCounteract.Load(new MemoryStream(array));
					ItemCounteract.Counteracts.Add(itemCounteract.ID, itemCounteract);
					itemCounteract.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.RESOURCE:
				while (num++ < num4)
				{
					ItemResource itemResource = new ItemResource();
					array = binaryReader.ReadBytes((int)num3);
					itemResource.Load(new MemoryStream(array));
					ItemResource.Resources.Add(itemResource.ID, itemResource);
					itemResource.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.QUEST:
				while (num++ < num4)
				{
					ItemQuest itemQuest = new ItemQuest();
					array = binaryReader.ReadBytes((int)num3);
					itemQuest.Load(new MemoryStream(array));
					ItemQuest.Quests.Add(itemQuest.ID, itemQuest);
					itemQuest.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.ACCESSORY:
				while (num++ < num4)
				{
					ItemAccessory itemAccessory = new ItemAccessory();
					array = binaryReader.ReadBytes((int)num3);
					itemAccessory.Load(new MemoryStream(array));
					ItemAccessory.Accessories.Add(itemAccessory.ID, itemAccessory);
					itemAccessory.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.LIFE:
				while (num++ < num4)
				{
					ItemLife itemLife = new ItemLife();
					array = binaryReader.ReadBytes((int)num3);
					itemLife.Load(new MemoryStream(array));
					ItemLife.Lifes.Add(itemLife.ID, itemLife);
					itemLife.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.MUSIC:
				while (num++ < num4)
				{
					ItemMusic itemMusic = new ItemMusic();
					array = binaryReader.ReadBytes((int)num3);
					itemMusic.Load(new MemoryStream(array));
					ItemMusic.Musics.Add(itemMusic.ID, itemMusic);
					itemMusic.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.PASS:
				while (num++ < num4)
				{
					ItemPass itemPass = new ItemPass();
					array = binaryReader.ReadBytes((int)num3);
					itemPass.Load(new MemoryStream(array));
					ItemPass.Passes.Add(itemPass.ID, itemPass);
					itemPass.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.MONEY:
				while (num++ < num4)
				{
					ItemMoney itemMoney = new ItemMoney();
					array = binaryReader.ReadBytes((int)num3);
					itemMoney.Load(new MemoryStream(array));
					ItemMoney.Moneys.Add(itemMoney.ID, itemMoney);
					itemMoney.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.SOCKET:
				while (num++ < num4)
				{
					ItemSocket itemSocket = new ItemSocket();
					array = binaryReader.ReadBytes((int)num3);
					itemSocket.Load(new MemoryStream(array));
					ItemSocket.Sockets.Add(itemSocket.ID, itemSocket);
					itemSocket.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.BOX:
				while (num++ < num4)
				{
					ItemBox itemBox = new ItemBox();
					array = binaryReader.ReadBytes((int)num3);
					itemBox.Load(new MemoryStream(array));
					ItemBox.Boxes.Add(itemBox.ID, itemBox);
					itemBox.AddToItemRankMap();
					num2++;
				}
				continue;
			case ItemType.BOXKEY:
				while (num++ < num4)
				{
					ItemBoxKey itemBoxKey = new ItemBoxKey();
					array = binaryReader.ReadBytes((int)num3);
					itemBoxKey.Load(new MemoryStream(array));
					ItemBoxKey.BoxKeys.Add(itemBoxKey.ID, itemBoxKey);
					itemBoxKey.AddToItemRankMap();
					num2++;
				}
				continue;
			}
			while (num++ < num4)
			{
				array = binaryReader.ReadBytes((int)num3);
				num2++;
			}
		}
		while (binaryReader.BaseStream.Position != binaryReader.BaseStream.Length);
		s.Close();
	}

	public void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(toolName);
		binaryWriter.Write(version1);
		binaryWriter.Write(version2);
		updateTime.Save(s);
		binaryWriter.Write(userName);
		binaryWriter.Write(comName);
		binaryWriter.Write((sbyte)0);
		binaryWriter.Write(ItemWeapon.Size);
		binaryWriter.Write((short)ItemWeapon.Weapons.Count);
		binaryWriter.Write(ItemWeapon.Weapons.Keys.Max());
		foreach (ItemWeapon value in ItemWeapon.Weapons.Values)
		{
			value.Save(s);
		}
		binaryWriter.Write((sbyte)1);
		binaryWriter.Write(ItemCloth.Size);
		binaryWriter.Write((short)ItemCloth.Clothes.Count);
		binaryWriter.Write(ItemCloth.Clothes.Keys.Max());
		foreach (ItemCloth value2 in ItemCloth.Clothes.Values)
		{
			value2.Save(s);
		}
		binaryWriter.Write((sbyte)2);
		binaryWriter.Write(ItemBook.Size);
		binaryWriter.Write((short)ItemBook.Books.Count);
		binaryWriter.Write(ItemBook.Books.Keys.Max());
		foreach (ItemBook value3 in ItemBook.Books.Values)
		{
			value3.Save(s);
		}
		binaryWriter.Write((sbyte)3);
		binaryWriter.Write(ItemPotion.Size);
		binaryWriter.Write((short)ItemPotion.Potions.Count);
		binaryWriter.Write(ItemPotion.Potions.Keys.Max());
		foreach (ItemPotion value4 in ItemPotion.Potions.Values)
		{
			value4.Save(s);
		}
		binaryWriter.Write((sbyte)4);
		binaryWriter.Write(ItemConsuming.Size);
		binaryWriter.Write((short)ItemConsuming.Consumings.Count);
		binaryWriter.Write(ItemConsuming.Consumings.Keys.Max());
		foreach (ItemConsuming value5 in ItemConsuming.Consumings.Values)
		{
			value5.Save(s);
		}
		binaryWriter.Write((sbyte)5);
		binaryWriter.Write(ItemElixir.Size);
		binaryWriter.Write((short)ItemElixir.Elixirs.Count);
		binaryWriter.Write(ItemElixir.Elixirs.Keys.Max());
		foreach (ItemElixir value6 in ItemElixir.Elixirs.Values)
		{
			value6.Save(s);
		}
		binaryWriter.Write((sbyte)6);
		binaryWriter.Write(ItemCounteract.Size);
		binaryWriter.Write((short)ItemCounteract.Counteracts.Count);
		binaryWriter.Write((short)((ItemCounteract.Counteracts.Count != 0) ? ItemCounteract.Counteracts.Keys.Max() : 0));
		foreach (ItemCounteract value7 in ItemCounteract.Counteracts.Values)
		{
			value7.Save(s);
		}
		binaryWriter.Write((sbyte)7);
		binaryWriter.Write(ItemResource.Size);
		binaryWriter.Write((short)ItemResource.Resources.Count);
		binaryWriter.Write(ItemResource.Resources.Keys.Max());
		foreach (ItemResource value8 in ItemResource.Resources.Values)
		{
			value8.Save(s);
		}
		binaryWriter.Write((sbyte)8);
		binaryWriter.Write(ItemQuest.Size);
		binaryWriter.Write((short)ItemQuest.Quests.Count);
		binaryWriter.Write(ItemQuest.Quests.Keys.Max());
		foreach (ItemQuest value9 in ItemQuest.Quests.Values)
		{
			value9.Save(s);
		}
		binaryWriter.Write((sbyte)9);
		binaryWriter.Write(ItemAccessory.Size);
		binaryWriter.Write((short)ItemAccessory.Accessories.Count);
		binaryWriter.Write(ItemAccessory.Accessories.Keys.Max());
		foreach (ItemAccessory value10 in ItemAccessory.Accessories.Values)
		{
			value10.Save(s);
		}
		binaryWriter.Write((sbyte)10);
		binaryWriter.Write(ItemLife.Size);
		binaryWriter.Write((short)ItemLife.Lifes.Count);
		binaryWriter.Write(ItemLife.Lifes.Keys.Max());
		foreach (ItemLife value11 in ItemLife.Lifes.Values)
		{
			value11.Save(s);
		}
		binaryWriter.Write((sbyte)11);
		binaryWriter.Write(ItemMusic.Size);
		binaryWriter.Write((short)ItemMusic.Musics.Count);
		binaryWriter.Write((short)((ItemMusic.Musics.Keys.Count != 0) ? ItemMusic.Musics.Keys.Max() : 0));
		foreach (ItemMusic value12 in ItemMusic.Musics.Values)
		{
			value12.Save(s);
		}
		binaryWriter.Write((sbyte)12);
		binaryWriter.Write(ItemPass.Size);
		binaryWriter.Write((short)ItemPass.Passes.Count);
		binaryWriter.Write((short)((ItemPass.Passes.Keys.Count != 0) ? ItemPass.Passes.Keys.Max() : 0));
		foreach (ItemPass value13 in ItemPass.Passes.Values)
		{
			value13.Save(s);
		}
		binaryWriter.Write((sbyte)14);
		binaryWriter.Write(ItemSocket.Size);
		binaryWriter.Write((short)ItemSocket.Sockets.Count);
		binaryWriter.Write(ItemSocket.Sockets.Keys.Max());
		foreach (ItemSocket value14 in ItemSocket.Sockets.Values)
		{
			value14.Save(s);
		}
		binaryWriter.Write((sbyte)15);
		binaryWriter.Write(ItemWeapon.Size);
		binaryWriter.Write((short)ItemWeapon.Weapons2.Count);
		binaryWriter.Write(ItemWeapon.Weapons2.Keys.Max());
		foreach (ItemWeapon value15 in ItemWeapon.Weapons2.Values)
		{
			value15.Save(s);
		}
		binaryWriter.Write((sbyte)16);
		binaryWriter.Write(ItemCloth.Size);
		binaryWriter.Write((short)ItemCloth.Clothes2.Count);
		binaryWriter.Write(ItemCloth.Clothes2.Keys.Max());
		foreach (ItemCloth value16 in ItemCloth.Clothes2.Values)
		{
			value16.Save(s);
		}
		binaryWriter.Write((sbyte)17);
		binaryWriter.Write(ItemBox.Size);
		binaryWriter.Write((short)ItemBox.Boxes.Count);
		binaryWriter.Write(ItemBox.Boxes.Keys.Max());
		foreach (ItemBox value17 in ItemBox.Boxes.Values)
		{
			value17.Save(s);
		}
		binaryWriter.Write((sbyte)18);
		binaryWriter.Write(ItemBoxKey.Size);
		binaryWriter.Write((short)ItemBoxKey.BoxKeys.Count);
		binaryWriter.Write(ItemBoxKey.BoxKeys.Keys.Max());
		foreach (ItemBoxKey value18 in ItemBoxKey.BoxKeys.Values)
		{
			value18.Save(s);
		}
		binaryWriter.Write((sbyte)19);
		binaryWriter.Write(ItemWeapon.Size);
		binaryWriter.Write((short)ItemWeapon.Weapons3.Count);
		binaryWriter.Write(ItemWeapon.Weapons3.Keys.Max());
		foreach (ItemWeapon value19 in ItemWeapon.Weapons3.Values)
		{
			value19.Save(s);
		}
		binaryWriter.Write((sbyte)20);
		binaryWriter.Write(ItemCloth.Size);
		binaryWriter.Write((short)ItemCloth.Clothes3.Count);
		binaryWriter.Write(ItemCloth.Clothes3.Keys.Max());
		foreach (ItemCloth value20 in ItemCloth.Clothes3.Values)
		{
			value20.Save(s);
		}
		s.Close();
	}

	public string GetInfo()
	{
		string text = "";
		text += ToolName;
		text = text + "\nVersion: " + version1 + "." + version2 + "\n";
		text += updateTime.GetDate();
		text += "\n";
		text = text + UserName + "\n";
		return text + CompanyName;
	}
}
