using System;
using System.Drawing;
using ItemTableReader;
using ItemTableReader.Properties;

class TestLifeIcon
{
    static void Main()
    {
        try
        {
            // 测试Life图标加载
            var life = new ItemLife();
            life.ID = 1;
            life.IconIndex = 1;
            
            Console.WriteLine("Testing Life icon loading...");
            
            // 测试Icons.Life属性
            var lifeIcon = Icons.Life;
            if (lifeIcon != null)
            {
                Console.WriteLine($"Life icon loaded successfully: {lifeIcon.Width}x{lifeIcon.Height}");
            }
            else
            {
                Console.WriteLine("Life icon is null");
            }
            
            // 测试SetIcon方法
            var icon = ItemParser.SetIcon(life);
            if (icon != null)
            {
                Console.WriteLine($"SetIcon returned: {icon.Width}x{icon.Height}");
            }
            else
            {
                Console.WriteLine("SetIcon returned null");
            }
            
            Console.WriteLine("Test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
