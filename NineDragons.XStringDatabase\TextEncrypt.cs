namespace NineDragons.XStringDatabase;

public class TextEncrypt
{
	public static byte[] BikeyXor(byte[] buffer, byte[] key)
	{
		return BikeyXor(buffer, GetPaddedByteStringLength(buffer), key);
	}

	public static byte[] BikeyXor(byte[] buffer, int len, byte[] key)
	{
		for (int i = 0; i < len; i++)
		{
			buffer[i] ^= key[i % 2];
		}
		return buffer;
	}

	public static int GetPaddedByteStringLength(byte[] str)
	{
		for (int i = 0; i < str.Length; i += 2)
		{
			if (str[i] == 0)
			{
				return i;
			}
		}
		return str.Length;
	}
}
