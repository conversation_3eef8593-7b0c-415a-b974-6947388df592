using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace NineDragons.XStringDatabase;

public class XString : INotifyPropertyChanged
{
	private int _resourceIndex;

	private List<byte[]> _textString = new List<byte[]>();

	private List<int> _textStringLength = new List<int>();

	private List<int> _parameterOrder = new List<int>();

	public int ResourceIndex
	{
		get
		{
			return _resourceIndex;
		}
		set
		{
			_resourceIndex = value;
			NotifyPropertyChanged("ResourceIndex");
		}
	}

	public List<byte[]> TextString
	{
		get
		{
			return _textString;
		}
		set
		{
			_textString = value;
			NotifyPropertyChanged("TextString");
		}
	}

	public List<int> TextStringLength
	{
		get
		{
			return _textStringLength;
		}
		set
		{
			_textStringLength = value;
			NotifyPropertyChanged("TextStringLength");
		}
	}

	public List<int> ParameterOrder
	{
		get
		{
			return _parameterOrder;
		}
		set
		{
			_parameterOrder = value;
			NotifyPropertyChanged("ParameterOrder");
		}
	}

	public event PropertyChangedEventHandler PropertyChanged;

	public string UnicodeName(int x = 0)
	{
		return Encoding.Unicode.GetString(_textString[x]);
	}

	public XString()
	{
	}

	public XString(int resourceIndex, byte[] textString, int parameterOrder, int textStringLength)
	{
		_resourceIndex = resourceIndex;
		_textString.Add(textString);
		_textStringLength.Add(textStringLength);
		_parameterOrder.Add(parameterOrder);
	}

	private void NotifyPropertyChanged(string name)
	{
		if (this.PropertyChanged != null)
		{
			this.PropertyChanged(this, new PropertyChangedEventArgs(name));
		}
	}
}
