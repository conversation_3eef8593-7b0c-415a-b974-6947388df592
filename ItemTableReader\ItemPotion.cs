using System.IO;

namespace ItemTableReader;

internal class ItemPotion : ItemBase
{
	private static uint XSD_START_INDEX = 25000u;

	private uint _xsdName;

	private uint _xsdInfo;

	public sbyte cClan;

	public short sApplyValue;

	public sbyte cApplyType;

	public short sEffectId;

	public short sInCEffectId;

	public short sVisualEfId;

	public short sCooldown;

	public short sReqLevel;

	public short sSimgi;

	public short sJungGi;

	private static Map<ItemPotion> mapPotions = new Map<ItemPotion>();

	public uint xsdInfo;

	public static uint Size => 77u;

	public static Map<ItemPotion> Potions => mapPotions;

	public void updateXsdName()
	{
		_xsdName = base.XsdName + XSD_START_INDEX;
	}

	public void updateXsdInfo()
	{
		_xsdInfo = xsdInfo + XSD_START_INDEX;
	}

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		ItemRank = binaryReader.ReadUInt16();
		base.Grade = binaryReader.ReadByte();
		_xsdInfo = binaryReader.ReadUInt32();
		base.XsdItemInfo = _xsdInfo - XSD_START_INDEX;
		int xsdItemInfo = (int)base.XsdItemInfo;
		if (XsdManager.Maps["PotionInfo"].ContainsKey(xsdItemInfo))
		{
			base.Description = XsdManager.Maps["PotionInfo"][xsdItemInfo];
		}
		unknownBytes.AddRange(binaryReader.ReadBytes(9));
		base.Price = binaryReader.ReadUInt32();
		unknownBytes.AddRange(binaryReader.ReadBytes(8));
		_xsdName = binaryReader.ReadUInt32();
		base.XsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		xsdItemInfo = (int)base.XsdName;
		if (XsdManager.Maps["PotionName"].ContainsKey(xsdItemInfo))
		{
			base.Name = XsdManager.Maps["PotionName"][xsdItemInfo];
		}
		cClan = binaryReader.ReadSByte();
		sApplyValue = binaryReader.ReadInt16();
		cApplyType = binaryReader.ReadSByte();
		sEffectId = binaryReader.ReadInt16();
		sInCEffectId = binaryReader.ReadInt16();
		sVisualEfId = binaryReader.ReadInt16();
		base.Quality = binaryReader.ReadByte();
		base.Quality2 = binaryReader.ReadByte();
		sCooldown = binaryReader.ReadInt16();
		sReqLevel = binaryReader.ReadInt16();
		sSimgi = binaryReader.ReadInt16();
		sJungGi = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(13));
		unknownBytes.AddRange(binaryReader.ReadBytes(4));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(ItemRank);
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(_xsdInfo);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 9);
		num += 9;
		binaryWriter.Write(base.Price);
		binaryWriter.Write(unknownBytes.ToArray(), num, 8);
		num += 8;
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(cClan);
		binaryWriter.Write(sApplyValue);
		binaryWriter.Write(cApplyType);
		binaryWriter.Write(sEffectId);
		binaryWriter.Write(sInCEffectId);
		binaryWriter.Write(sVisualEfId);
		binaryWriter.Write(base.Quality);
		binaryWriter.Write(base.Quality2);
		binaryWriter.Write(sCooldown);
		binaryWriter.Write(sReqLevel);
		binaryWriter.Write(sSimgi);
		binaryWriter.Write(sJungGi);
		binaryWriter.Write(unknownBytes.ToArray(), num, 13);
		num += 13;
		binaryWriter.Write(unknownBytes.ToArray(), num, 4);
		num += 4;
	}
}
