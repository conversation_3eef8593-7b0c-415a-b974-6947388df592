using System.IO;

namespace ItemTableReader;

internal class ItemElixir : ItemBase
{
	private static uint XSD_START_INDEX = 35000u;

	private uint _xsdName;

	private uint _xsdInfo;

	private static Map<ItemElixir> mapElixirs = new Map<ItemElixir>();

	public uint xsdName;

	public uint xsdInfo;

	private sbyte thirdType;

	private byte step;

	private byte level;

	private ushort minValue;

	private ushort maxValue;

	private byte intox;

	private ushort cooldownDelay;

	private ushort successRate;

	private ushort[] bodyType = new ushort[8];

	private byte visualEff;

	private ushort[] tmp = new ushort[4];

	public static uint Size => 88u;

	public sbyte ThirdType => thirdType;

	public byte Step => step;

	public byte Level => level;

	public ushort MinValue => minValue;

	public ushort MaxValue => maxValue;

	public byte Intoxication => intox;

	public byte VisualEffect => visualEff;

	public ushort Cooldown => cooldownDelay;

	public ushort SuccessRate => successRate;

	public static Map<ItemElixir> Elixirs => mapElixirs;

	public override void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		_xsdName = binaryReader.ReadUInt32();
		thirdType = binaryReader.ReadSByte();
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		ItemRank = binaryReader.ReadUInt16();
		base.Grade = binaryReader.ReadByte();
		base.Quality = binaryReader.ReadByte();
		base.Quality2 = binaryReader.ReadByte();
		_xsdInfo = binaryReader.ReadUInt32();
		xsdName = _xsdName - XSD_START_INDEX;
		xsdInfo = _xsdInfo - XSD_START_INDEX;
		int key = (int)xsdName;
		if (XsdManager.Maps["ElixirName"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["ElixirName"], XsdManager.TableNames["ElixirName"]);
		}
		if (XsdManager.Maps["ElixirInfo"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["ElixirInfo"], XsdManager.TableNames["ElixirInfo"]);
		}
		if (XsdManager.Maps["ElixirName"].ContainsKey(key))
		{
			base.Name = XsdManager.Maps["ElixirName"][key];
		}
		key = (int)xsdInfo;
		if (XsdManager.Maps["ElixirInfo"].ContainsKey(key))
		{
			base.Description = XsdManager.Maps["ElixirInfo"][key];
		}
		step = binaryReader.ReadByte();
		level = binaryReader.ReadByte();
		minValue = binaryReader.ReadUInt16();
		maxValue = binaryReader.ReadUInt16();
		cooldownDelay = binaryReader.ReadUInt16();
		successRate = binaryReader.ReadUInt16();
		for (int i = 0; i < 8; i++)
		{
			bodyType[i] = binaryReader.ReadUInt16();
		}
		intox = binaryReader.ReadByte();
		visualEff = binaryReader.ReadByte();
		for (int j = 0; j < 4; j++)
		{
			tmp[j] = binaryReader.ReadUInt16();
		}
		base.ApplyClan = binaryReader.ReadSByte();
		base.ClanPoint1 = binaryReader.ReadInt32();
		base.ClanPoint2 = binaryReader.ReadInt32();
		base.Price = binaryReader.ReadUInt32();
		base.BlockDrop = binaryReader.ReadBoolean();
		BlockTrade = binaryReader.ReadByte();
		BlockNpcSell = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(7));
		base.CashCheck = binaryReader.ReadSByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		base.BlockStorage = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(3));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(_xsdName);
		binaryWriter.Write(ThirdType);
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(ItemRank);
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(base.Quality);
		binaryWriter.Write(base.Quality2);
		binaryWriter.Write(_xsdInfo);
		binaryWriter.Write(step);
		binaryWriter.Write(level);
		binaryWriter.Write(minValue);
		binaryWriter.Write(maxValue);
		binaryWriter.Write(cooldownDelay);
		binaryWriter.Write(successRate);
		for (int i = 0; i < 8; i++)
		{
			binaryWriter.Write(bodyType[i]);
		}
		binaryWriter.Write(intox);
		binaryWriter.Write(visualEff);
		for (int j = 0; j < 4; j++)
		{
			binaryWriter.Write(tmp[j]);
		}
		binaryWriter.Write(base.ApplyClan);
		binaryWriter.Write(base.ClanPoint1);
		binaryWriter.Write(base.ClanPoint2);
		binaryWriter.Write(base.Price);
		binaryWriter.Write(base.BlockDrop);
		binaryWriter.Write(BlockTrade);
		binaryWriter.Write(BlockNpcSell);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 7);
		num += 7;
		binaryWriter.Write(base.CashCheck);
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(base.BlockStorage);
		binaryWriter.Write(unknownBytes.ToArray(), num, 3);
		num += 3;
	}
}
