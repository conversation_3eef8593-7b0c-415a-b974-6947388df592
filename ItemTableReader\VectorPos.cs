using System.IO;

namespace ItemTableReader;

internal class VectorPos
{
	internal float x;

	internal float y;

	internal float z;

	public VectorPos()
	{
	}

	public VectorPos(VectorPos v)
	{
		x = v.x;
		y = v.y;
		z = v.z;
	}

	public void Load(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		x = binaryReader.ReadSingle();
		y = binaryReader.ReadSingle();
		z = binaryReader.ReadSingle();
	}

	public void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(x);
		binaryWriter.Write(y);
		binaryWriter.Write(z);
	}
}
