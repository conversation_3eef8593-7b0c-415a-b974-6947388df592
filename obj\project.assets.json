{"version": 3, "targets": {".NETFramework,Version=v4.5": {"System.Resources.Extensions/4.6.0": {"type": "package"}}, ".NETFramework,Version=v4.5/win-x86": {"System.Resources.Extensions/4.6.0": {"type": "package"}}}, "libraries": {"System.Resources.Extensions/4.6.0": {"sha512": "6aVCk8oTFZNT3Tx1jjiPi6+aipiJ3qMZYttAREKTRJidP50YvNeOn4PXrqzfA5qC23fLReq2JYp+nJwzj62HGw==", "type": "package", "path": "system.resources.extensions/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/System.Resources.Extensions.dll", "lib/netstandard2.0/System.Resources.Extensions.xml", "ref/netstandard2.0/System.Resources.Extensions.dll", "ref/netstandard2.0/System.Resources.Extensions.xml", "system.resources.extensions.4.6.0.nupkg.sha512", "system.resources.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.5": ["System.Resources.Extensions >= 4.6.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\SERVER\\工具源码\\item\\ItemTableReader.csproj", "projectName": "ItemTableReader", "projectPath": "D:\\SERVER\\工具源码\\item\\ItemTableReader.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\SERVER\\工具源码\\item\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net45": {"targetAlias": "net45", "dependencies": {"System.Resources.Extensions": {"target": "Package", "version": "[4.6.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}, "logs": [{"code": "NU1202", "level": "Error", "message": "包 System.Resources.Extensions 4.6.0 与 net45 (.NETFramework,Version=v4.5) 不兼容。 包 System.Resources.Extensions 4.6.0 支持: netstandard2.0 (.NETStandard,Version=v2.0)", "libraryId": "System.Resources.Extensions", "targetGraphs": [".NETFramework,Version=v4.5"]}, {"code": "NU1202", "level": "Error", "message": "包 System.Resources.Extensions 4.6.0 与 net45 (.NETFramework,Version=v4.5) / win-x86 不兼容。 包 System.Resources.Extensions 4.6.0 支持: netstandard2.0 (.NETStandard,Version=v2.0)", "libraryId": "System.Resources.Extensions", "targetGraphs": [".NETFramework,Version=v4.5/win-x86"]}]}