using System;
using System.CodeDom.Compiler;
using System.Collections;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ItemTableReader.Properties
{
    [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [DebuggerNonUserCode]
    [CompilerGenerated]
    internal class ClothIcons
    {
        private static ResourceManager resourceMan;
        private static CultureInfo resourceCulture;

        [EditorBrowsable(EditorBrowsableState.Advanced)]
        internal static ResourceManager ResourceManager
        {
            get
            {
                if (ClothIcons.resourceMan == null)
                {
                    ResourceManager temp = new ResourceManager("ItemTableReader.Properties.ClothIcons", typeof(ClothIcons).Assembly);
                    ClothIcons.resourceMan = temp;
                }
                return ClothIcons.resourceMan;
            }
        }

        [EditorBrowsable(EditorBrowsableState.Advanced)]
        internal static CultureInfo Culture
        {
            get
            {
                return ClothIcons.resourceCulture;
            }
            set
            {
                ClothIcons.resourceCulture = value;
            }
        }

        // 上衣(UPPER)图标
        internal static Bitmap Item_Clo_00a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_00a", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_01a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_01a", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_02a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_02a", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_03a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_03a", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_04a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_04a", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_05a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_05a", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_06a
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_06a", resourceCulture);
            }
        }

        // 裤子(LOWER)图标
        internal static Bitmap Item_Clo_00b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_00b", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_01b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_01b", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_02b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_02b", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_03b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_03b", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_04b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_04b", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_05b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_05b", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_06b
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_06b", resourceCulture);
            }
        }

        // 手套(GLOVES)图标
        internal static Bitmap Item_Clo_00c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_00c", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_01c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_01c", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_02c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_02c", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_03c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_03c", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_04c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_04c", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_05c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_05c", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_06c
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_06c", resourceCulture);
            }
        }

        // 鞋子(SHOES)图标
        internal static Bitmap Item_Clo_00d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_00d", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_01d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_01d", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_02d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_02d", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_03d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_03d", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_04d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_04d", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_05d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_05d", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_06d
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_06d", resourceCulture);
            }
        }

        // 帽子(CAP)图标
        internal static Bitmap Item_Clo_00e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_00e", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_01e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_01e", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_02e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_02e", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_03e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_03e", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_04e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_04e", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_05e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_05e", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_06e
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_06e", resourceCulture);
            }
        }

        // 背包(BACKPACK)图标
        internal static Bitmap Item_Clo_00h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_00h", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_01h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_01h", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_02h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_02h", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_03h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_03h", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_04h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_04h", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_05h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_05h", resourceCulture);
            }
        }

        internal static Bitmap Item_Clo_06h
        {
            get
            {
                return (Bitmap)ResourceManager.GetObject("Item_Clo_06h", resourceCulture);
            }
        }

        /// <summary>
        /// 获取指定类型和门派的服装图标
        /// </summary>
        /// <param name="clothType">服装类型：a=上衣, b=裤子, c=手套, d=鞋子, e=帽子, h=背包</param>
        /// <param name="clan">门派：0=浪人, 1=丐帮, 2=秘宫, 3=少林, 4=绿林, 5=武当, 6=魔教</param>
        /// <returns>服装图标位图，如果不存在则返回null</returns>
        internal static Bitmap GetClothIcon(char clothType, int clan)
        {
            if (clan < 0 || clan > 6)
            {
                System.Diagnostics.Debug.WriteLine($"Invalid clan: {clan}");
                return null;
            }

            string resourceName = $"Item_Clo_0{clan}{clothType}";
            try
            {
                return (Bitmap)ResourceManager.GetObject(resourceName, resourceCulture);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading cloth icon: {ex.Message}");
                return null;
            }
        }

        internal ClothIcons()
        {
        }

        public static void DebugAvailableResources()
        {
            ResourceManager rm = ResourceManager;
            ResourceSet rs = rm.GetResourceSet(CultureInfo.CurrentUICulture, true, true);
            if (rs != null)
            {
                Console.WriteLine("ClothIcons Available Resources:");
                foreach (DictionaryEntry entry in rs)
                {
                    string resourceKey = entry.Key.ToString();
                    object resource = entry.Value;
                    Console.WriteLine($"Resource Key: {resourceKey}, Type: {resource?.GetType().Name ?? "null"}");
                    Debug.WriteLine($"Resource Key: {resourceKey}, Type: {resource?.GetType().Name ?? "null"}");
                }
            }
            else
            {
                Console.WriteLine("No resources found in ClothIcons.");
                Debug.WriteLine("No resources found in ClothIcons.");
            }
        }
    }
}
