using System.IO;
using System.Linq;

namespace ItemTableReader;

internal class ItemWeapon : ItemBase
{
	private static Map<ItemWeapon> mapWeapons = new Map<ItemWeapon>();

	private static Map<ItemWeapon> mapWeapons2 = new Map<ItemWeapon>();

	private static Map<ItemWeapon> mapWeapons3 = new Map<ItemWeapon>();

	public ushort xsdNick;

	public ushort xsdAddTo;

	public uint xsdName;

	private uint xsdItemInfo;

	public sbyte thirdType;

	public short minDmg;

	public short maxDmg;

	public ushort durabiliy;

	public sbyte balance;

	public sbyte attackRate;

	public short critRate;

	public sbyte hardness;

	public short level;

	public short mainStat;

	public short secondaryStat;

	public sbyte rank;

	public short hiddenID;

	public byte sockets;

	public VectorPos[] vecItemPosition = new VectorPos[4];

	public Effect[] effects = new Effect[5];

	public Special[] specials = new Special[4];

	public byte reduceLevel;

	public byte maxSlots;

	public bool statTrade;

	private string nickname;

	private string addTo;

	public static uint Size => 226u;

	public static Map<ItemWeapon> Weapons => mapWeapons;

	public static Map<ItemWeapon> Weapons2 => mapWeapons2;

	public static Map<ItemWeapon> Weapons3 => mapWeapons3;

	public new string FullName => $"({base.ID})\t" + ((string.IsNullOrEmpty(addTo) || addTo == "null") ? "" : (addTo + " ")) + ((string.IsNullOrEmpty(nickname) || nickname == "null") ? "" : (nickname + " ")) + base.Name;

	public string Nickname => nickname;

	public string AddTo => addTo;

	public ItemWeapon()
	{
	}

	public ItemWeapon(ItemWeapon w)
	{
		base.Type = w.Type;
		base.SecondType = w.SecondType;
		xsdNick = w.xsdNick;
		xsdAddTo = w.xsdAddTo;
		xsdName = w.xsdName;
		xsdItemInfo = w.xsdItemInfo;
		thirdType = w.thirdType;
		minDmg = w.minDmg;
		maxDmg = w.maxDmg;
		durabiliy = w.durabiliy;
		balance = w.balance;
		attackRate = w.attackRate;
		critRate = w.critRate;
		hardness = w.hardness;
		base.ApplyClan = w.ApplyClan;
		base.ClanPoint1 = w.ClanPoint1;
		base.ClanPoint2 = w.ClanPoint2;
		level = w.level;
		mainStat = w.mainStat;
		secondaryStat = w.secondaryStat;
		base.Price = w.Price;
		rank = w.rank;
		hiddenID = w.hiddenID;
		sockets = w.sockets;
		base.CashCheck = w.CashCheck;
		base.Time = w.Time;
		base.ModelIndex = w.ModelIndex;
		base.IconIndex = w.IconIndex;
		vecItemPosition = new VectorPos[4];
		for (int i = 0; i < 4; i++)
		{
			vecItemPosition[i] = new VectorPos(w.vecItemPosition[i]);
		}
		effects = new Effect[5];
		for (int j = 0; j < 5; j++)
		{
			effects[j] = new Effect(w.effects[j]);
		}
		specials = new Special[4];
		for (int k = 0; k < 4; k++)
		{
			specials[k] = new Special(w.specials[k]);
		}
		reduceLevel = w.reduceLevel;
		BlockNpcSell = w.BlockNpcSell;
		BlockTrade = w.BlockTrade;
		base.BlockStorage = w.BlockStorage;
		maxSlots = w.maxSlots;
		statTrade = w.statTrade;
		unknownBytes = w.unknownBytes.ToList();
		switch (base.Type)
		{
		case 0:
			base.ID = mapWeapons.Keys.Max();
			mapWeapons.Add(++base.ID, this);
			break;
		case 15:
			base.ID = mapWeapons2.Keys.Max();
			mapWeapons2.Add(++base.ID, this);
			break;
		case 19:
			base.ID = mapWeapons3.Keys.Max();
			mapWeapons3.Add(++base.ID, this);
			break;
		}
	}

	public override void Load(Stream s)
	{
		if (XsdManager.WeaponInfo.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.WeaponInfo, "ItemTable_SeedInfoWeapon");
		}
		if (XsdManager.WeaponNames.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.WeaponNames, "ItemTable_SeedWeapon");
		}
		if (XsdManager.AddTos.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.AddTos, "ItemTable_AddTo");
		}
		if (XsdManager.Nicknames.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Nicknames, "ItemTable_Nickname");
		}
		if (XsdManager.EffectsName.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.EffectsName, "CharacterState_name");
		}
		if (XsdManager.EffectsInfo.Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.EffectsInfo, "CharacterState_info");
		}
		BinaryReader binaryReader = new BinaryReader(s);
		base.Type = binaryReader.ReadSByte();
		base.SecondType = binaryReader.ReadSByte();
		base.ID = binaryReader.ReadInt16();
		xsdNick = binaryReader.ReadUInt16();
		xsdAddTo = binaryReader.ReadUInt16();
		xsdName = binaryReader.ReadUInt32();
		thirdType = binaryReader.ReadSByte();
		xsdItemInfo = binaryReader.ReadUInt32();
		if (XsdManager.WeaponNames.ContainsKey((int)xsdName))
		{
			base.Name = XsdManager.WeaponNames[(int)xsdName];
		}
		if (XsdManager.WeaponInfo.ContainsKey((int)xsdItemInfo))
		{
			base.Description = XsdManager.WeaponInfo[(int)xsdItemInfo];
		}
		if (XsdManager.AddTos.ContainsKey(xsdAddTo))
		{
			addTo = XsdManager.AddTos[xsdAddTo];
		}
		if (XsdManager.Nicknames.ContainsKey(xsdNick))
		{
			nickname = XsdManager.Nicknames[xsdNick];
		}
		minDmg = binaryReader.ReadInt16();
		maxDmg = binaryReader.ReadInt16();
		durabiliy = binaryReader.ReadUInt16();
		balance = binaryReader.ReadSByte();
		attackRate = binaryReader.ReadSByte();
		critRate = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(3));
		hardness = binaryReader.ReadSByte();
		level = binaryReader.ReadInt16();
		mainStat = binaryReader.ReadInt16();
		secondaryStat = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(6));
		base.ApplyClan = binaryReader.ReadSByte();
		base.ClanPoint1 = binaryReader.ReadInt32();
		base.ClanPoint2 = binaryReader.ReadInt32();
		base.Price = binaryReader.ReadUInt32();
		unknownBytes.AddRange(binaryReader.ReadBytes(4));
		rank = binaryReader.ReadSByte();
		hiddenID = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		sockets = binaryReader.ReadByte();
		unknownBytes.Add(binaryReader.ReadByte());
		base.ModelIndex = binaryReader.ReadInt16();
		base.IconIndex = binaryReader.ReadInt16();
		unknownBytes.AddRange(binaryReader.ReadBytes(6));
		base.Grade = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		for (int i = 0; i < 4; i++)
		{
			vecItemPosition[i] = new VectorPos();
			vecItemPosition[i].Load(new MemoryStream(binaryReader.ReadBytes(12)));
		}
		for (int j = 0; j < 5; j++)
		{
			effects[j] = new Effect();
			effects[j].Load(binaryReader.ReadBytes(8));
		}
		for (int k = 0; k < 4; k++)
		{
			specials[k] = new Special();
			specials[k].Load(binaryReader.ReadBytes(8));
		}
		unknownBytes.AddRange(binaryReader.ReadBytes(2));
		base.BlockDrop = binaryReader.ReadBoolean();
		BlockTrade = binaryReader.ReadByte();
		BlockNpcSell = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(7));
		base.CashCheck = binaryReader.ReadSByte();
		base.Time = binaryReader.ReadInt16();
		base.BlockStorage = binaryReader.ReadBoolean();
		unknownBytes.AddRange(binaryReader.ReadBytes(4));
		maxSlots = binaryReader.ReadByte();
		unknownBytes.Add(binaryReader.ReadByte());
		reduceLevel = binaryReader.ReadByte();
		unknownBytes.AddRange(binaryReader.ReadBytes(3));
	}

	public override void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(base.Type);
		binaryWriter.Write(base.SecondType);
		binaryWriter.Write(base.ID);
		binaryWriter.Write(xsdNick);
		binaryWriter.Write(xsdAddTo);
		binaryWriter.Write(xsdName);
		binaryWriter.Write(thirdType);
		binaryWriter.Write(xsdItemInfo);
		binaryWriter.Write(minDmg);
		binaryWriter.Write(maxDmg);
		binaryWriter.Write(durabiliy);
		binaryWriter.Write(balance);
		binaryWriter.Write(attackRate);
		binaryWriter.Write(critRate);
		int num = 0;
		binaryWriter.Write(unknownBytes.ToArray(), num, 3);
		num += 3;
		binaryWriter.Write(hardness);
		binaryWriter.Write(level);
		binaryWriter.Write(mainStat);
		binaryWriter.Write(secondaryStat);
		binaryWriter.Write(unknownBytes.ToArray(), num, 6);
		num += 6;
		binaryWriter.Write(base.ApplyClan);
		binaryWriter.Write(base.ClanPoint1);
		binaryWriter.Write(base.ClanPoint2);
		binaryWriter.Write(base.Price);
		binaryWriter.Write(unknownBytes.ToArray(), num, 4);
		num += 4;
		binaryWriter.Write(rank);
		binaryWriter.Write(hiddenID);
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(sockets);
		binaryWriter.Write(unknownBytes.ToArray(), num, 1);
		num++;
		binaryWriter.Write(base.ModelIndex);
		binaryWriter.Write(base.IconIndex);
		binaryWriter.Write(unknownBytes.ToArray(), num, 6);
		num += 6;
		binaryWriter.Write(base.Grade);
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		for (int i = 0; i < 4; i++)
		{
			vecItemPosition[i].Save(s);
		}
		for (int j = 0; j < 5; j++)
		{
			effects[j].Save(s);
		}
		for (int k = 0; k < 4; k++)
		{
			specials[k].Save(s);
		}
		binaryWriter.Write(unknownBytes.ToArray(), num, 2);
		num += 2;
		binaryWriter.Write(base.BlockDrop);
		binaryWriter.Write(BlockTrade);
		binaryWriter.Write(BlockNpcSell);
		binaryWriter.Write(unknownBytes.ToArray(), num, 7);
		num += 7;
		binaryWriter.Write(base.CashCheck);
		binaryWriter.Write(base.Time);
		binaryWriter.Write(base.BlockStorage);
		binaryWriter.Write(unknownBytes.ToArray(), num, 4);
		num += 4;
		binaryWriter.Write(maxSlots);
		binaryWriter.Write(unknownBytes.ToArray(), num, 1);
		num++;
		binaryWriter.Write(reduceLevel);
		binaryWriter.Write(unknownBytes.ToArray(), num, 3);
		num += 3;
	}
}
