using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace ItemTableReader.Forms;

public class FormPotion : Form
{
	private List<KeyValuePair<int, string>> effects = new List<KeyValuePair<int, string>>();

	private IContainer components;

	private ListBox listBoxPotions;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPage2;

	private ByteViewer byteViewer = new ByteViewer();

	private SplitContainer splitContainer1;

	private TableLayoutPanel tableLayoutPanel1;

	private Label label1;

	private Label label2;

	private Label label3;

	private Label label4;

	private ComboBox comboBoxType;

	private TextBox textBoxValue;

	private ComboBox comboBoxEffect;

	private Label label5;

	private NumericUpDown textBoxCooldown;

	private TextBox textBoxRank;

	private Label label6;

	private Label label7;

	private TextBox textBoxSecondType;

	private Label label8;

	private TextBox textBoxVisEffect;

	private TextBox textBoxPrice;

	private Label label9;

	private CheckBox checkBoxBlockTrade;

	private Label label10;

	private ComboBox unknownByteIndex;

	private TextBox unknownByteValue;

	private Label label11;

	private Label label27;

	private TextBox textBoxXSDIndex;

	private TextBox textBoxXSDInfo;

	public FormPotion()
	{
		InitializeComponent();
		for (int i = 0; i < 34; i++)
		{
			unknownByteIndex.Items.Add(i.ToString());
		}
	}

	private void FormPotion_Load(object sender, EventArgs e)
	{
		byteViewer.Dock = DockStyle.Fill;
		tabPage2.Controls.Add(byteViewer);
		listBoxPotions.DisplayMember = "FullName";
		listBoxPotions.ValueMember = "ID";
		listBoxPotions.BeginUpdate();
		foreach (ItemPotion value2 in ItemPotion.Potions.Values)
		{
			listBoxPotions.Items.Add(value2);
		}
		listBoxPotions.EndUpdate();
		int num = Enum.GetValues(typeof(PotionSecondType)).Cast<int>().Max();
		for (int i = 0; i <= num; i++)
		{
			ComboBox.ObjectCollection items = comboBoxType.Items;
			PotionSecondType potionSecondType = (PotionSecondType)i;
			items.Add(potionSecondType.ToString());
		}
		foreach (KeyValuePair<int, string> item in XsdManager.EffectsName)
		{
			effects.Add(item);
		}
		EventHandler value = comboBoxEffect_SelectedIndexChanged;
		comboBoxEffect.SelectedIndexChanged -= value;
		comboBoxEffect.DisplayMember = "Value";
		comboBoxEffect.ValueMember = "Key";
		comboBoxEffect.DataSource = effects;
		comboBoxEffect.SelectedIndex = -1;
		comboBoxEffect.SelectedIndexChanged += value;
	}

	private void listBoxPotions_SelectedIndexChanged(object sender, EventArgs e)
	{
		ItemPotion potion = listBoxPotions.SelectedItem as ItemPotion;
		if (potion != null)
		{
			byteViewer.Dock = DockStyle.Fill;
			byteViewer.SetBytes(potion.unknownBytes.ToArray());
			textBoxXSDIndex.Text = potion.XsdName.ToString();
			textBoxXSDInfo.Text = potion.xsdInfo.ToString();
			textBoxValue.TextChanged -= textBoxValue_TextChanged;
			textBoxCooldown.ValueChanged -= textBoxCooldown_ValueChanged;
			textBoxRank.TextChanged -= textBoxRank_TextChanged;
			textBoxSecondType.TextChanged -= textBoxSecondType_TextChanged;
			textBoxVisEffect.TextChanged -= textBoxVisEffect_TextChanged;
			textBoxPrice.TextChanged -= textBoxPrice_TextChanged;
			comboBoxType.SelectedIndexChanged -= comboBoxType_SelectedIndexChanged;
			comboBoxEffect.SelectedIndexChanged -= comboBoxEffect_SelectedIndexChanged;
			checkBoxBlockTrade.CheckStateChanged -= checkBoxBlockTrade_CheckedChanged;
			textBoxValue.Text = potion.sApplyValue + ((potion.cApplyType != 0) ? "%" : "");
			textBoxCooldown.Text = potion.sCooldown.ToString();
			textBoxRank.Text = potion.ItemRank.ToString();
			textBoxSecondType.Text = potion.SecondType.ToString();
			textBoxVisEffect.Text = potion.sVisualEfId.ToString();
			textBoxPrice.Text = potion.Price.ToString();
			checkBoxBlockTrade.Checked = potion.BlockTrade != 0;
			if (potion.SecondType <= Enum.GetValues(typeof(PotionSecondType)).Cast<int>().Max())
			{
				comboBoxType.SelectedIndex = potion.SecondType;
			}
			else
			{
				comboBoxType.SelectedIndex = -1;
			}
			comboBoxEffect.SelectedIndex = effects.FindIndex((KeyValuePair<int, string> el) => el.Key == potion.sEffectId);
			textBoxValue.TextChanged += textBoxValue_TextChanged;
			textBoxCooldown.ValueChanged += textBoxCooldown_ValueChanged;
			textBoxRank.TextChanged += textBoxRank_TextChanged;
			textBoxSecondType.TextChanged += textBoxSecondType_TextChanged;
			textBoxVisEffect.TextChanged += textBoxVisEffect_TextChanged;
			textBoxPrice.TextChanged += textBoxPrice_TextChanged;
			comboBoxType.SelectedIndexChanged += comboBoxType_SelectedIndexChanged;
			comboBoxEffect.SelectedIndexChanged += comboBoxEffect_SelectedIndexChanged;
			checkBoxBlockTrade.CheckStateChanged += checkBoxBlockTrade_CheckedChanged;
			if (unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < potion.unknownBytes.Count)
			{
				unknownByteValue.Text = potion.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
	}

	private void textBoxValue_TextChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion)
		{
			if (textBoxValue.Text.EndsWith("%") && short.TryParse(textBoxValue.Text.Substring(0, textBoxValue.Text.Length - 1), out var result))
			{
				itemPotion.cApplyType = 1;
				itemPotion.sApplyValue = result;
			}
			else if (short.TryParse(textBoxValue.Text, out result))
			{
				itemPotion.cApplyType = 0;
				itemPotion.sApplyValue = result;
			}
		}
	}

	private void textBoxRank_TextChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion && ushort.TryParse(textBoxRank.Text, out var result))
		{
			itemPotion.ItemRank = result;
		}
	}

	private void textBoxCooldown_ValueChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion)
		{
			itemPotion.sCooldown = (short)textBoxCooldown.Value;
		}
	}

	private void comboBoxType_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion)
		{
			itemPotion.SecondType = (sbyte)comboBoxType.SelectedIndex;
		}
	}

	private void comboBoxEffect_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion && comboBoxEffect.SelectedItem != null)
		{
			itemPotion.sEffectId = (short)effects[comboBoxEffect.SelectedIndex].Key;
		}
	}

	private void textBoxValue_KeyPress(object sender, KeyPressEventArgs e)
	{
		e.Handled = !char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar) && e.KeyChar != '%';
	}

	private void FormPotion_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Up)
		{
			if (listBoxPotions.SelectedIndex > 0)
			{
				listBoxPotions.SelectedIndex--;
			}
		}
		else if (e.KeyCode == Keys.Down && listBoxPotions.SelectedIndex < listBoxPotions.Items.Count - 1)
		{
			listBoxPotions.SelectedIndex++;
		}
	}

	private void splitContainer1_KeyDown(object sender, KeyEventArgs e)
	{
		FormPotion_KeyDown(sender, e);
		e.SuppressKeyPress = true;
		splitContainer1.IsSplitterFixed = true;
	}

	private void splitContainer1_KeyUp(object sender, KeyEventArgs e)
	{
		splitContainer1.IsSplitterFixed = false;
	}

	private void textBoxSecondType_TextChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion && sbyte.TryParse(textBoxSecondType.Text, out var result))
		{
			itemPotion.SecondType = result;
		}
	}

	private void textBoxVisEffect_TextChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion && short.TryParse(textBoxVisEffect.Text, out var result))
		{
			itemPotion.sVisualEfId = result;
		}
	}

	private void textBoxPrice_TextChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion && uint.TryParse(textBoxPrice.Text, out var result))
		{
			itemPotion.Price = result;
		}
	}

	private void checkBoxBlockTrade_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxPotions.SelectedItem is ItemPotion itemPotion)
		{
			itemPotion.BlockTrade = (byte)(checkBoxBlockTrade.Checked ? 1 : 0);
		}
	}

	private void unknownByteIndex_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxPotions.SelectedItem is ItemPotion itemPotion && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemPotion.unknownBytes.Count)
			{
				unknownByteValue.Text = itemPotion.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxPotions.SelectedItem is ItemPotion itemPotion && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemPotion.unknownBytes.Count)
			{
				itemPotion.unknownBytes[unknownByteIndex.SelectedIndex] = Convert.ToByte(unknownByteValue.Text);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemPotion obj = listBoxPotions.SelectedItem as ItemPotion;
			obj.XsdName = Convert.ToUInt16(textBoxXSDIndex.Text);
			obj.updateXsdName();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDInfo_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemPotion obj = listBoxPotions.SelectedItem as ItemPotion;
			obj.xsdInfo = Convert.ToUInt16(textBoxXSDInfo.Text);
			obj.updateXsdInfo();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.listBoxPotions = new System.Windows.Forms.ListBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.label7 = new System.Windows.Forms.Label();
            this.textBoxRank = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.comboBoxType = new System.Windows.Forms.ComboBox();
            this.textBoxValue = new System.Windows.Forms.TextBox();
            this.comboBoxEffect = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.textBoxCooldown = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.textBoxSecondType = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.textBoxVisEffect = new System.Windows.Forms.TextBox();
            this.textBoxPrice = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.checkBoxBlockTrade = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.unknownByteIndex = new System.Windows.Forms.ComboBox();
            this.unknownByteValue = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.textBoxXSDIndex = new System.Windows.Forms.TextBox();
            this.textBoxXSDInfo = new System.Windows.Forms.TextBox();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textBoxCooldown)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listBoxPotions
            // 
            this.listBoxPotions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxPotions.FormattingEnabled = true;
            this.listBoxPotions.ItemHeight = 12;
            this.listBoxPotions.Location = new System.Drawing.Point(0, 0);
            this.listBoxPotions.Margin = new System.Windows.Forms.Padding(2);
            this.listBoxPotions.Name = "listBoxPotions";
            this.listBoxPotions.Size = new System.Drawing.Size(173, 317);
            this.listBoxPotions.TabIndex = 0;
            this.listBoxPotions.SelectedIndexChanged += new System.EventHandler(this.listBoxPotions_SelectedIndexChanged);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(2);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(342, 317);
            this.tabControl1.TabIndex = 1;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.tableLayoutPanel1);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(2);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(2);
            this.tabPage1.Size = new System.Drawing.Size(334, 291);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Details";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.label7, 0, 5);
            this.tableLayoutPanel1.Controls.Add(this.textBoxRank, 1, 4);
            this.tableLayoutPanel1.Controls.Add(this.label1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.label2, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.label3, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.label4, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.comboBoxType, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.textBoxValue, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.comboBoxEffect, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.label5, 2, 1);
            this.tableLayoutPanel1.Controls.Add(this.textBoxCooldown, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.label6, 0, 4);
            this.tableLayoutPanel1.Controls.Add(this.textBoxSecondType, 1, 5);
            this.tableLayoutPanel1.Controls.Add(this.label8, 0, 6);
            this.tableLayoutPanel1.Controls.Add(this.textBoxVisEffect, 1, 6);
            this.tableLayoutPanel1.Controls.Add(this.textBoxPrice, 1, 7);
            this.tableLayoutPanel1.Controls.Add(this.label9, 0, 7);
            this.tableLayoutPanel1.Controls.Add(this.checkBoxBlockTrade, 0, 9);
            this.tableLayoutPanel1.Controls.Add(this.label10, 0, 8);
            this.tableLayoutPanel1.Controls.Add(this.unknownByteIndex, 1, 8);
            this.tableLayoutPanel1.Controls.Add(this.unknownByteValue, 2, 8);
            this.tableLayoutPanel1.Controls.Add(this.label11, 0, 10);
            this.tableLayoutPanel1.Controls.Add(this.label27, 0, 11);
            this.tableLayoutPanel1.Controls.Add(this.textBoxXSDIndex, 1, 10);
            this.tableLayoutPanel1.Controls.Add(this.textBoxXSDInfo, 1, 11);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(2, 2);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 14;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 18F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 24F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 13F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 7F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(330, 287);
            this.tableLayoutPanel1.TabIndex = 0;
            // 
            // label7
            // 
            this.label7.Location = new System.Drawing.Point(3, 118);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(54, 21);
            this.label7.TabIndex = 13;
            this.label7.Text = "2nd Type";
            this.label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxRank
            // 
            this.textBoxRank.Location = new System.Drawing.Point(99, 97);
            this.textBoxRank.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.textBoxRank.Name = "textBoxRank";
            this.textBoxRank.Size = new System.Drawing.Size(80, 21);
            this.textBoxRank.TabIndex = 11;
            this.textBoxRank.TextChanged += new System.EventHandler(this.textBoxRank_TextChanged);
            // 
            // label1
            // 
            this.label1.Location = new System.Drawing.Point(3, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(31, 22);
            this.label1.TabIndex = 0;
            this.label1.Text = "Type";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.label1.Click += new System.EventHandler(this.label1_Click);
            // 
            // label2
            // 
            this.label2.Location = new System.Drawing.Point(3, 23);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(34, 22);
            this.label2.TabIndex = 1;
            this.label2.Text = "Value";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label3
            // 
            this.label3.Location = new System.Drawing.Point(3, 47);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 22);
            this.label3.TabIndex = 2;
            this.label3.Text = "Effect";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label4
            // 
            this.label4.Location = new System.Drawing.Point(3, 70);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(54, 22);
            this.label4.TabIndex = 3;
            this.label4.Text = "Cooldown";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // comboBoxType
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.comboBoxType, 2);
            this.comboBoxType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxType.FormattingEnabled = true;
            this.comboBoxType.Location = new System.Drawing.Point(99, 3);
            this.comboBoxType.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.comboBoxType.Name = "comboBoxType";
            this.comboBoxType.Size = new System.Drawing.Size(228, 20);
            this.comboBoxType.TabIndex = 4;
            this.comboBoxType.SelectedIndexChanged += new System.EventHandler(this.comboBoxType_SelectedIndexChanged);
            // 
            // textBoxValue
            // 
            this.textBoxValue.Location = new System.Drawing.Point(99, 26);
            this.textBoxValue.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.textBoxValue.Name = "textBoxValue";
            this.textBoxValue.Size = new System.Drawing.Size(80, 21);
            this.textBoxValue.TabIndex = 5;
            this.textBoxValue.TextChanged += new System.EventHandler(this.textBoxValue_TextChanged);
            this.textBoxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.textBoxValue_KeyPress);
            // 
            // comboBoxEffect
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.comboBoxEffect, 2);
            this.comboBoxEffect.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxEffect.FormattingEnabled = true;
            this.comboBoxEffect.Location = new System.Drawing.Point(99, 50);
            this.comboBoxEffect.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.comboBoxEffect.Name = "comboBoxEffect";
            this.comboBoxEffect.Size = new System.Drawing.Size(228, 20);
            this.comboBoxEffect.TabIndex = 6;
            this.comboBoxEffect.SelectedIndexChanged += new System.EventHandler(this.comboBoxEffect_SelectedIndexChanged);
            // 
            // label5
            // 
            this.label5.Location = new System.Drawing.Point(226, 23);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(101, 22);
            this.label5.TabIndex = 8;
            this.label5.Text = "(percentage or absolute value)";
            this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxCooldown
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.textBoxCooldown, 2);
            this.textBoxCooldown.Location = new System.Drawing.Point(99, 73);
            this.textBoxCooldown.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.textBoxCooldown.Maximum = new decimal(new int[] {
            -1,
            -1,
            -1,
            0});
            this.textBoxCooldown.Name = "textBoxCooldown";
            this.textBoxCooldown.Size = new System.Drawing.Size(80, 21);
            this.textBoxCooldown.TabIndex = 9;
            this.textBoxCooldown.ValueChanged += new System.EventHandler(this.textBoxCooldown_ValueChanged);
            // 
            // label6
            // 
            this.label6.Location = new System.Drawing.Point(3, 94);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(54, 21);
            this.label6.TabIndex = 12;
            this.label6.Text = "Item rank";
            this.label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxSecondType
            // 
            this.textBoxSecondType.Location = new System.Drawing.Point(99, 121);
            this.textBoxSecondType.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.textBoxSecondType.Name = "textBoxSecondType";
            this.textBoxSecondType.Size = new System.Drawing.Size(80, 21);
            this.textBoxSecondType.TabIndex = 14;
            this.textBoxSecondType.TextChanged += new System.EventHandler(this.textBoxSecondType_TextChanged);
            // 
            // label8
            // 
            this.label8.Location = new System.Drawing.Point(3, 142);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(60, 18);
            this.label8.TabIndex = 15;
            this.label8.Text = "Vis. Effect";
            this.label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxVisEffect
            // 
            this.textBoxVisEffect.Location = new System.Drawing.Point(99, 145);
            this.textBoxVisEffect.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.textBoxVisEffect.Name = "textBoxVisEffect";
            this.textBoxVisEffect.Size = new System.Drawing.Size(80, 21);
            this.textBoxVisEffect.TabIndex = 16;
            this.textBoxVisEffect.TextChanged += new System.EventHandler(this.textBoxVisEffect_TextChanged);
            // 
            // textBoxPrice
            // 
            this.textBoxPrice.Location = new System.Drawing.Point(99, 169);
            this.textBoxPrice.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.textBoxPrice.Name = "textBoxPrice";
            this.textBoxPrice.Size = new System.Drawing.Size(80, 21);
            this.textBoxPrice.TabIndex = 18;
            this.textBoxPrice.TextChanged += new System.EventHandler(this.textBoxPrice_TextChanged);
            // 
            // label9
            // 
            this.label9.Location = new System.Drawing.Point(3, 166);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(60, 18);
            this.label9.TabIndex = 17;
            this.label9.Text = "Price";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBoxBlockTrade
            // 
            this.checkBoxBlockTrade.AutoSize = true;
            this.checkBoxBlockTrade.Location = new System.Drawing.Point(3, 211);
            this.checkBoxBlockTrade.Name = "checkBoxBlockTrade";
            this.checkBoxBlockTrade.Size = new System.Drawing.Size(90, 16);
            this.checkBoxBlockTrade.TabIndex = 19;
            this.checkBoxBlockTrade.Text = "Can\'t trade";
            this.checkBoxBlockTrade.UseVisualStyleBackColor = true;
            this.checkBoxBlockTrade.CheckedChanged += new System.EventHandler(this.checkBoxBlockTrade_CheckedChanged);
            // 
            // label10
            // 
            this.label10.Location = new System.Drawing.Point(3, 190);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(77, 18);
            this.label10.TabIndex = 20;
            this.label10.Text = "UnknownBytes";
            this.label10.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // unknownByteIndex
            // 
            this.unknownByteIndex.FormattingEnabled = true;
            this.unknownByteIndex.Location = new System.Drawing.Point(99, 193);
            this.unknownByteIndex.Name = "unknownByteIndex";
            this.unknownByteIndex.Size = new System.Drawing.Size(121, 20);
            this.unknownByteIndex.TabIndex = 21;
            this.unknownByteIndex.SelectedIndexChanged += new System.EventHandler(this.unknownByteIndex_SelectedIndexChanged);
            // 
            // unknownByteValue
            // 
            this.unknownByteValue.Location = new System.Drawing.Point(226, 193);
            this.unknownByteValue.Margin = new System.Windows.Forms.Padding(3, 3, 3, 0);
            this.unknownByteValue.Name = "unknownByteValue";
            this.unknownByteValue.Size = new System.Drawing.Size(80, 21);
            this.unknownByteValue.TabIndex = 22;
            this.unknownByteValue.TextChanged += new System.EventHandler(this.unknownByteValue_TextChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(3, 234);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(53, 12);
            this.label11.TabIndex = 27;
            this.label11.Text = "XSDIndex";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(2, 261);
            this.label27.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(53, 12);
            this.label27.TabIndex = 69;
            this.label27.Text = "XSD Info";
            // 
            // textBoxXSDIndex
            // 
            this.textBoxXSDIndex.Location = new System.Drawing.Point(99, 237);
            this.textBoxXSDIndex.Name = "textBoxXSDIndex";
            this.textBoxXSDIndex.Size = new System.Drawing.Size(100, 21);
            this.textBoxXSDIndex.TabIndex = 70;
            this.textBoxXSDIndex.TextChanged += new System.EventHandler(this.textBoxXSDIndex_TextChanged);
            // 
            // textBoxXSDInfo
            // 
            this.textBoxXSDInfo.Location = new System.Drawing.Point(98, 263);
            this.textBoxXSDInfo.Margin = new System.Windows.Forms.Padding(2);
            this.textBoxXSDInfo.Name = "textBoxXSDInfo";
            this.textBoxXSDInfo.Size = new System.Drawing.Size(76, 21);
            this.textBoxXSDInfo.TabIndex = 71;
            this.textBoxXSDInfo.TextChanged += new System.EventHandler(this.textBoxXSDInfo_TextChanged);
            // 
            // tabPage2
            // 
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(2);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(2);
            this.tabPage2.Size = new System.Drawing.Size(334, 291);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Unknown bytes";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.listBoxPotions);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.tabControl1);
            this.splitContainer1.Size = new System.Drawing.Size(519, 317);
            this.splitContainer1.SplitterDistance = 173;
            this.splitContainer1.TabIndex = 2;
            this.splitContainer1.TabStop = false;
            this.splitContainer1.KeyDown += new System.Windows.Forms.KeyEventHandler(this.splitContainer1_KeyDown);
            this.splitContainer1.KeyUp += new System.Windows.Forms.KeyEventHandler(this.splitContainer1_KeyUp);
            // 
            // FormPotion
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(519, 317);
            this.Controls.Add(this.splitContainer1);
            this.Margin = new System.Windows.Forms.Padding(2);
            this.Name = "FormPotion";
            this.Text = "FormPotion";
            this.Load += new System.EventHandler(this.FormPotion_Load);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.FormPotion_KeyDown);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textBoxCooldown)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

	}

    private void label1_Click(object sender, EventArgs e)
    {

    }
}
