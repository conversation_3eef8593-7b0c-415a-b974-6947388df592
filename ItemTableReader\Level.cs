namespace ItemTableReader;

internal class Level
{
	private static string[] levels = new string[20]
	{
		"Losing Self", "Gathering Chi", "Opening Chi", "Revolving Chakra", "Raising Light", "Five Dragons", "Sun and Moon", "Golden Blossom", "Elemental Crown", "Floral Crown",
		"Crimson Snake", "Golden Lotus", "Humble Master", "Little Master", "Hermit", "Projected Soul", "Dissolved Body", "Immortal", "Heaven's Gate", "Heavenly Immortal"
	};

	public static string Get(short lv)
	{
		if (lv > 240 || lv < 0)
		{
			return "";
		}
		if (lv == 0)
		{
			return "None 0";
		}
		if (lv % 12 == 0)
		{
			return levels[lv / 12 - 1] + " 12";
		}
		return levels[lv / 12] + " " + ((lv % 12 == 0) ? 12 : (lv % 12));
	}
}
