using System.Collections.Generic;
using System.ComponentModel;

namespace NineDragons.XStringDatabase;

public class XStringCollection
{
	private BindingList<XString> _rows = new BindingList<XString>();

	public BindingList<XString> Rows => _rows;

	public void Add(int id, int parameterOrder, int length, byte[] textString)
	{
		XString xString = new XString();
		xString.ResourceIndex = id;
		xString.ParameterOrder.Add(parameterOrder);
		xString.TextStringLength.Add(length);
		xString.TextString.Add(textString);
		_rows.Add(xString);
	}

	public void Add(int id, List<int> parameterOrder, List<int> length, List<byte[]> textString)
	{
		XString item = new XString
		{
			ResourceIndex = id,
			ParameterOrder = parameterOrder,
			TextStringLength = length,
			TextString = textString
		};
		_rows.Add(item);
	}
}
