using System;
using System.Collections.Generic;
using System.IO;

namespace ItemTableReader;

public abstract class ItemBase : IItem
{
	public List<byte> unknownBytes = new List<byte>();

	public byte BlockTrade;

	public bool BlockNpcSell;

	public static Map<ItemBase> Items { get; set; }

	public string FullName => $"({ID})\t{Name}";

	public sbyte Type { get; set; }

	public sbyte SecondType { get; set; }

	public short ID { get; set; }

	public short ModelIndex { get; set; }

	public short IconIndex { get; set; }

	public virtual ushort ItemRank { get; set; }

	public byte Grade { get; set; }

	public byte Quality { get; set; }

	public byte Quality2 { get; set; }

	public uint Price { get; set; }

	public string Name { get; set; }

	public string Description { get; set; }

	public bool BlockDrop { get; set; }

	public bool BlockStorage { get; set; }

	public sbyte CashCheck { get; set; }

	public short Time { get; set; }

	public sbyte ApplyClan { get; set; }

	public int ClanPoint1 { get; set; }

	public int ClanPoint2 { get; set; }

	public byte Fame { get; set; }

	public uint XsdName { get; set; }

	public uint XsdItemInfo { get; set; }

	// Convenience properties for data binding
	public bool CanDrop => !BlockDrop;

	public bool CanStore => !BlockStorage;

	public bool CanNPC => !BlockNpcSell;

	public bool CanTrade => BlockTrade == 0;

	public int Contrib1 => ClanPoint1;

	public int Contrib2 => ClanPoint2;

	public abstract void Load(Stream s);

	public abstract void Save(Stream s);

	public void AddToItemRankMap()
	{
		if (!ItemParser.ItemRanks.ContainsKey(ItemRank))
		{
			ItemParser.ItemRanks[ItemRank] = new List<ItemBase>();
		}
		if (ItemParser.ItemRanks[ItemRank] == null)
		{
			ItemParser.ItemRanks[ItemRank] = new List<ItemBase>();
		}
		ItemParser.ItemRanks[ItemRank].Add(this);
	}

	public static void SaveMap(Map<IItem> map, Type type, ItemType itemType, Stream s)
	{
	}
}
