using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormClothes : Form
{
	private List<KeyValuePair<int, string>> nicknames = new List<KeyValuePair<int, string>>();

	private static ItemCloth tempCloth = new ItemCloth();

	private static ItemCloth tempCloth2 = new ItemCloth();

	private IContainer components;

	private ComboBox comboBox1;

	private ByteViewer byteViewer = new ByteViewer();

	private BindingSource effectBindingSource;

	private BindingSource itemClothBindingSource;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPage2;

	private TabPage tabPage3;

	private DataGridView dataGridView1;

	private DataGridViewTextBoxColumn iDDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn valueDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn descriptionDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn nameDataGridViewTextBoxColumn;

	private ListBox listBoxClothes;

	private Label label3;

	private Label label2;

	private Label label1;

	private Label label4;

	private Label label5;

	private Label label6;

	private Label label7;

	private Label label8;

	private Label label9;

	private Label label10;

	private Label label11;

	private Label label12;

	private Label label13;

	private Label label14;

	private Label label15;

	private Label label16;

	private Label label17;

	private Label label18;

	private TextBox textBox19;

	private TextBox textBox18;

	private TextBox textBox17;

	private TextBox textBox16;

	private TextBox textBox15;

	private TextBox textBox14;

	private TextBox textBox13;

	private TextBox textBox12;

	private TextBox textBox11;

	private TextBox textBox10;

	private TextBox textBox9;

	private TextBox textBox8;

	private TextBox textBox7;

	private TextBox textBox6;

	private TextBox textBox5;

	private TextBox textBox4;

	private TextBox textBox3;

	private TextBox textBox2;

	private TextBox textBox1;

	private Label label19;

	private Label lbLevelDisplay;

	private TextBox textBox20;

	private TextBox textBox21;

	private Label label20;

	private Label label21;

	private TextBox textBox22;

	private Label label22;

	private CheckBox checkBoxDrop;

	private CheckBox checkBoxSellNPC;

	private CheckBox checkBoxTrade;

	private TextBox textBoxTime;

	private Label label23;

	private TextBox unknownByteValue;

	private ComboBox unknownByteIndex;

	private Label label24;

	private Button btn_deleteItem;

	private Button btn_pasteItem;

	private Button btn_copyItem;

	private Button btn_pasteEffects;

	private Button btn_copyEffects;

	private Label label25;

	private ComboBox comboBoxNickname;

	private TextBox multipleItems;

	private Button btn_pasteToMultiple;

	private GroupBox groupBox2;

	private GroupBox groupBox1;

	private TextBox textBoxXSDIndex;

	private Label label26;

	private TextBox textBoxXSDInfo;

	private Label label27;

	public FormClothes()
	{
		InitializeComponent();
		comboBox1.SelectedIndex = 0;
		for (int i = 0; i < 88; i++)
		{
			unknownByteIndex.Items.Add(i.ToString());
		}
	}

	private void FormClothes_Load(object sender, EventArgs e)
	{
		comboBox1.SelectedIndex = 0;
		byteViewer.Dock = DockStyle.Fill;
		tabPage2.Controls.Add(byteViewer);
		foreach (KeyValuePair<int, string> nickname in XsdManager.Nicknames)
		{
			nicknames.Add(nickname);
		}
		comboBoxNickname.DisplayMember = "Value";
		comboBoxNickname.ValueMember = "Key";
		comboBoxNickname.DataSource = nicknames;
	}

	private void listBoxClothes_SelectedIndexChanged(object sender, EventArgs e)
	{
		ItemCloth cloth = listBoxClothes.SelectedItem as ItemCloth;
		comboBoxNickname.SelectedIndex = nicknames.FindIndex((KeyValuePair<int, string> el) => el.Key == cloth.xsdNick);
		byteViewer.SetBytes(cloth.unknownBytes.ToArray());
		dataGridView1.DataSource = cloth.Effects;
		textBoxXSDIndex.Text = cloth.xsdName.ToString();
		textBoxXSDInfo.Text = cloth.xsdInfo.ToString();
		textBox1.Text = cloth.Type.ToString();
		textBox2.Text = cloth.SecondType.ToString();
		textBox3.Text = cloth.ModelIndex.ToString();
		textBox4.Text = cloth.clan.ToString();
		textBox5.Text = cloth.sex.ToString();
		textBox6.Text = cloth.ApplyClan.ToString();
		textBox7.Text = cloth.characGrade.ToString();
		textBox8.Text = cloth.IconIndex.ToString();
		textBox9.Text = cloth.precedence.ToString();
		textBox10.Text = cloth.ClanPoint1.ToString();
		textBox11.Text = cloth.ClanPoint2.ToString();
		textBox12.Text = cloth.itemCase.ToString();
		textBox13.Text = cloth.def.ToString();
		textBox14.Text = cloth.durability.ToString();
		textBox15.Text = cloth.itemSet.ToString();
		textBox16.Text = cloth.level.ToString();
		textBox17.Text = cloth.Grade.ToString();
		textBox18.Text = cloth.Price.ToString();
		textBox19.Text = cloth.ItemRank.ToString();
		textBox20.Text = cloth.slots.ToString();
		textBox21.Text = cloth.maxSlots.ToString();
		textBox22.Text = cloth.pockets.ToString();
		textBoxTime.Text = cloth.time.ToString();
		if (unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < cloth.unknownBytes.Count)
		{
			unknownByteValue.Text = cloth.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
		}
		checkBoxDrop.CheckedChanged -= checkBoxDrop_CheckedChanged;
		checkBoxTrade.CheckStateChanged -= checkBoxTrade_CheckStateChanged;
		checkBoxSellNPC.CheckedChanged -= checkBoxSellNPC_CheckedChanged;
		checkBoxDrop.Checked = cloth.BlockDrop;
		checkBoxTrade.CheckState = (CheckState)cloth.cUserTrade;
		checkBoxSellNPC.Checked = cloth.BlockNpcSell;
		checkBoxDrop.CheckedChanged += checkBoxDrop_CheckedChanged;
		checkBoxTrade.CheckStateChanged += checkBoxTrade_CheckStateChanged;
		checkBoxSellNPC.CheckedChanged += checkBoxSellNPC_CheckedChanged;
	}

	private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		listBoxClothes.Items.Clear();
		Dictionary<short, ItemCloth> dictionary = null;
		dictionary = comboBox1.SelectedIndex switch
		{
			0 => ItemCloth.Clothes, 
			1 => ItemCloth.Clothes2, 
			2 => ItemCloth.Clothes3, 
			_ => ItemCloth.Clothes, 
		};
		listBoxClothes.BeginUpdate();
		foreach (KeyValuePair<short, ItemCloth> item in dictionary)
		{
			listBoxClothes.Items.Add(item.Value);
			listBoxClothes.DisplayMember = "FullName";
			listBoxClothes.ValueMember = "ID";
		}
		listBoxClothes.EndUpdate();
	}

	private void FormClothes_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Up)
		{
			if (listBoxClothes.SelectedIndex > 0)
			{
				listBoxClothes.SelectedIndex--;
			}
		}
		else if (e.KeyCode == Keys.Down && listBoxClothes.SelectedIndex < listBoxClothes.Items.Count - 1)
		{
			listBoxClothes.SelectedIndex++;
		}
	}

	private void textBox16_TextChanged(object sender, EventArgs e)
	{
		lbLevelDisplay.Text = Level.Get(Convert.ToInt16(textBox16.Text));
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).level = Convert.ToByte(textBox16.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox1_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).Type = Convert.ToSByte(textBox1.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox2_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).SecondType = Convert.ToSByte(textBox2.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox3_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).ModelIndex = Convert.ToInt16(textBox3.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox4_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).clan = Convert.ToSByte(textBox4.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox5_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).sex = Convert.ToByte(textBox5.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox6_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).ApplyClan = Convert.ToSByte(textBox6.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox7_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).characGrade = Convert.ToByte(textBox7.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox8_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).IconIndex = Convert.ToInt16(textBox8.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox9_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).precedence = Convert.ToSByte(textBox9.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox10_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).ClanPoint1 = Convert.ToInt32(textBox10.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox11_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).ClanPoint2 = Convert.ToInt32(textBox11.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox12_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).itemCase = Convert.ToInt16(textBox12.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox13_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).def = Convert.ToUInt16(textBox13.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox14_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).durability = Convert.ToUInt16(textBox14.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox15_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).itemSet = Convert.ToUInt16(textBox15.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox17_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).Grade = Convert.ToByte(textBox17.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox18_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).Price = Convert.ToUInt32(textBox18.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox19_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).ItemRank = Convert.ToUInt16(textBox19.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox20_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).slots = Convert.ToByte(textBox20.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox21_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).maxSlots = Convert.ToByte(textBox21.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBox22_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).pockets = Convert.ToByte(textBox22.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void dataGridView1_CellValueChanged(object sender, DataGridViewCellEventArgs e)
	{
		if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
		{
			try
			{
				int key = Convert.ToInt32(dataGridView1[0, e.RowIndex].Value.ToString());
				itemCloth.effects[e.RowIndex].Desc = XsdManager.EffectsInfo[key];
				itemCloth.effects[e.RowIndex].Name = XsdManager.EffectsName[key];
			}
			catch
			{
			}
			dataGridView1.Refresh();
		}
	}

	private void checkBoxDrop_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
		{
			itemCloth.BlockDrop = checkBoxDrop.Checked;
		}
	}

	private void checkBoxTrade_CheckStateChanged(object sender, EventArgs e)
	{
		if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
		{
			itemCloth.cUserTrade = (byte)checkBoxTrade.CheckState;
		}
	}

	private void checkBoxSellNPC_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
		{
			itemCloth.BlockNpcSell = checkBoxSellNPC.Checked;
		}
	}

	private void textBoxTime_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxClothes.SelectedItem as ItemCloth).time = Convert.ToUInt16(textBoxTime.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteIndex_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxClothes.SelectedItem is ItemCloth itemCloth && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemCloth.unknownBytes.Count)
			{
				unknownByteValue.Text = itemCloth.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxClothes.SelectedItem is ItemCloth itemCloth && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemCloth.unknownBytes.Count)
			{
				itemCloth.unknownBytes[unknownByteIndex.SelectedIndex] = Convert.ToByte(unknownByteValue.Text);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_deleteItem_Click(object sender, EventArgs e)
	{
		try
		{
			ItemCloth itemCloth = listBoxClothes.SelectedItem as ItemCloth;
			int selectedIndex = listBoxClothes.SelectedIndex;
			if (selectedIndex < 0)
			{
				return;
			}
			selectedIndex = ((selectedIndex == 0) ? 1 : (selectedIndex - 1));
			listBoxClothes.SelectedIndex = selectedIndex;
			listBoxClothes.Items.Remove(itemCloth);
			foreach (KeyValuePair<short, ItemCloth> item in ItemCloth.Clothes.ToList())
			{
				if (item.Value.Equals(itemCloth))
				{
					ItemCloth.Clothes.Remove(item.Key);
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_copyItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (!(listBoxClothes.SelectedItem is ItemCloth itemCloth))
			{
				return;
			}
			tempCloth.Type = itemCloth.Type;
			tempCloth.SecondType = itemCloth.SecondType;
			tempCloth.ModelIndex = itemCloth.ModelIndex;
			tempCloth.sex = itemCloth.sex;
			tempCloth.ApplyClan = itemCloth.ApplyClan;
			tempCloth.characGrade = itemCloth.characGrade;
			tempCloth.IconIndex = itemCloth.IconIndex;
			tempCloth.precedence = itemCloth.precedence;
			tempCloth.ClanPoint1 = itemCloth.ClanPoint1;
			tempCloth.ClanPoint2 = itemCloth.ClanPoint2;
			tempCloth.itemCase = itemCloth.itemCase;
			tempCloth.def = itemCloth.def;
			tempCloth.durability = itemCloth.durability;
			tempCloth.itemSet = itemCloth.itemSet;
			tempCloth.level = itemCloth.level;
			tempCloth.Grade = itemCloth.Grade;
			tempCloth.Price = itemCloth.Price;
			tempCloth.ItemRank = itemCloth.ItemRank;
			tempCloth.slots = itemCloth.slots;
			tempCloth.maxSlots = itemCloth.maxSlots;
			tempCloth.pockets = itemCloth.pockets;
			tempCloth.time = itemCloth.time;
			tempCloth.group = itemCloth.group;
			tempCloth.clan = itemCloth.clan;
			tempCloth.BlockDrop = itemCloth.BlockDrop;
			tempCloth.cUserTrade = itemCloth.cUserTrade;
			tempCloth.BlockNpcSell = itemCloth.BlockNpcSell;
			tempCloth.xsdName = itemCloth.xsdName;
			tempCloth.xsdInfo = itemCloth.xsdInfo;
			tempCloth.xsdNick = itemCloth.xsdNick;
			for (int i = 0; i < 5; i++)
			{
				tempCloth.effects[i] = itemCloth.effects[i];
			}
			if (tempCloth.unknownBytes.Count == 0)
			{
				byte item = 0;
				for (int j = 0; j < 88; j++)
				{
					tempCloth.unknownBytes.Add(item);
				}
			}
			for (int k = 0; k < itemCloth.unknownBytes.Count; k++)
			{
				tempCloth.unknownBytes[k] = itemCloth.unknownBytes[k];
			}
			btn_pasteItem.Enabled = true;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_pasteItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
			{
				itemCloth.Type = tempCloth.Type;
				itemCloth.SecondType = tempCloth.SecondType;
				itemCloth.ModelIndex = tempCloth.ModelIndex;
				itemCloth.sex = tempCloth.sex;
				itemCloth.ApplyClan = tempCloth.ApplyClan;
				itemCloth.characGrade = tempCloth.characGrade;
				itemCloth.IconIndex = tempCloth.IconIndex;
				itemCloth.precedence = tempCloth.precedence;
				itemCloth.ClanPoint1 = tempCloth.ClanPoint1;
				itemCloth.ClanPoint2 = tempCloth.ClanPoint2;
				itemCloth.itemCase = tempCloth.itemCase;
				itemCloth.def = tempCloth.def;
				itemCloth.durability = tempCloth.durability;
				itemCloth.itemSet = tempCloth.itemSet;
				itemCloth.level = tempCloth.level;
				itemCloth.Grade = tempCloth.Grade;
				itemCloth.Price = tempCloth.Price;
				itemCloth.ItemRank = tempCloth.ItemRank;
				itemCloth.slots = tempCloth.slots;
				itemCloth.maxSlots = tempCloth.maxSlots;
				itemCloth.pockets = tempCloth.pockets;
				itemCloth.time = tempCloth.time;
				itemCloth.group = tempCloth.group;
				itemCloth.clan = tempCloth.clan;
				itemCloth.BlockDrop = tempCloth.BlockDrop;
				itemCloth.cUserTrade = tempCloth.cUserTrade;
				itemCloth.BlockNpcSell = tempCloth.BlockNpcSell;
				itemCloth.xsdName = tempCloth.xsdName;
				itemCloth.xsdInfo = tempCloth.xsdInfo;
				itemCloth.xsdNick = tempCloth.xsdNick;
				for (int i = 0; i < 5; i++)
				{
					itemCloth.effects[i].ID = tempCloth.effects[i].ID;
					itemCloth.effects[i].Type = tempCloth.effects[i].Type;
					itemCloth.effects[i].Value = tempCloth.effects[i].Value;
					itemCloth.effects[i].Per = tempCloth.effects[i].Per;
					itemCloth.effects[i].Prob = tempCloth.effects[i].Prob;
				}
				for (int j = 0; j < tempCloth.unknownBytes.Count; j++)
				{
					itemCloth.unknownBytes[j] = tempCloth.unknownBytes[j];
				}
			}
			listBoxClothes_SelectedIndexChanged(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void copyEffects()
	{
		try
		{
			if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
			{
				for (int i = 0; i < 5; i++)
				{
					tempCloth2.effects[i] = itemCloth.effects[i];
				}
				btn_pasteEffects.Enabled = true;
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_copyEffects_Click(object sender, EventArgs e)
	{
		copyEffects();
	}

	private void pasteEffects(ItemCloth cloth)
	{
		if (cloth != null)
		{
			for (int i = 0; i < 5; i++)
			{
				cloth.effects[i].ID = tempCloth2.effects[i].ID;
				cloth.effects[i].Type = tempCloth2.effects[i].Type;
				cloth.effects[i].Value = tempCloth2.effects[i].Value;
				cloth.effects[i].Per = tempCloth2.effects[i].Per;
				cloth.effects[i].Prob = tempCloth2.effects[i].Prob;
			}
		}
	}

	private void btn_pasteEffects_Click(object sender, EventArgs e)
	{
		try
		{
			ItemCloth cloth = listBoxClothes.SelectedItem as ItemCloth;
			pasteEffects(cloth);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void comboBoxNickname_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxClothes.SelectedItem is ItemCloth itemCloth)
			{
				itemCloth.xsdNick = (ushort)nicknames[comboBoxNickname.SelectedIndex].Key;
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_pasteToMultiple_Click(object sender, EventArgs e)
	{
		copyEffects();
		try
		{
			string[] array = multipleItems.Text.Split();
			for (int i = 0; i < array.Length; i++)
			{
				short key = short.Parse(array[i]);
				if (ItemCloth.Clothes.ContainsKey(key))
				{
					ItemCloth cloth = ItemCloth.Clothes[key];
					pasteEffects(cloth);
				}
				if (ItemCloth.Clothes2.ContainsKey(key))
				{
					ItemCloth cloth2 = ItemCloth.Clothes2[key];
					pasteEffects(cloth2);
				}
				if (ItemCloth.Clothes3.ContainsKey(key))
				{
					ItemCloth cloth3 = ItemCloth.Clothes3[key];
					pasteEffects(cloth3);
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemCloth obj = listBoxClothes.SelectedItem as ItemCloth;
			obj.xsdName = Convert.ToUInt16(textBoxXSDIndex.Text);
			obj.updateXsdName();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDInfo_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemCloth obj = listBoxClothes.SelectedItem as ItemCloth;
			obj.xsdInfo = Convert.ToUInt16(textBoxXSDInfo.Text);
			obj.updateXsdInfo();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.comboBox1 = new System.Windows.Forms.ComboBox();
		this.tabControl1 = new System.Windows.Forms.TabControl();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.textBoxXSDIndex = new System.Windows.Forms.TextBox();
		this.label26 = new System.Windows.Forms.Label();
		this.multipleItems = new System.Windows.Forms.TextBox();
		this.btn_pasteToMultiple = new System.Windows.Forms.Button();
		this.groupBox2 = new System.Windows.Forms.GroupBox();
		this.btn_pasteEffects = new System.Windows.Forms.Button();
		this.btn_copyEffects = new System.Windows.Forms.Button();
		this.groupBox1 = new System.Windows.Forms.GroupBox();
		this.btn_pasteItem = new System.Windows.Forms.Button();
		this.btn_copyItem = new System.Windows.Forms.Button();
		this.comboBoxNickname = new System.Windows.Forms.ComboBox();
		this.label25 = new System.Windows.Forms.Label();
		this.btn_deleteItem = new System.Windows.Forms.Button();
		this.unknownByteValue = new System.Windows.Forms.TextBox();
		this.unknownByteIndex = new System.Windows.Forms.ComboBox();
		this.label24 = new System.Windows.Forms.Label();
		this.textBoxTime = new System.Windows.Forms.TextBox();
		this.label23 = new System.Windows.Forms.Label();
		this.checkBoxSellNPC = new System.Windows.Forms.CheckBox();
		this.checkBoxTrade = new System.Windows.Forms.CheckBox();
		this.checkBoxDrop = new System.Windows.Forms.CheckBox();
		this.textBox22 = new System.Windows.Forms.TextBox();
		this.label22 = new System.Windows.Forms.Label();
		this.textBox20 = new System.Windows.Forms.TextBox();
		this.textBox21 = new System.Windows.Forms.TextBox();
		this.label20 = new System.Windows.Forms.Label();
		this.label21 = new System.Windows.Forms.Label();
		this.lbLevelDisplay = new System.Windows.Forms.Label();
		this.textBox19 = new System.Windows.Forms.TextBox();
		this.textBox18 = new System.Windows.Forms.TextBox();
		this.textBox17 = new System.Windows.Forms.TextBox();
		this.textBox16 = new System.Windows.Forms.TextBox();
		this.textBox15 = new System.Windows.Forms.TextBox();
		this.textBox14 = new System.Windows.Forms.TextBox();
		this.textBox13 = new System.Windows.Forms.TextBox();
		this.textBox12 = new System.Windows.Forms.TextBox();
		this.textBox11 = new System.Windows.Forms.TextBox();
		this.textBox10 = new System.Windows.Forms.TextBox();
		this.textBox9 = new System.Windows.Forms.TextBox();
		this.textBox8 = new System.Windows.Forms.TextBox();
		this.textBox7 = new System.Windows.Forms.TextBox();
		this.textBox6 = new System.Windows.Forms.TextBox();
		this.textBox5 = new System.Windows.Forms.TextBox();
		this.textBox4 = new System.Windows.Forms.TextBox();
		this.textBox3 = new System.Windows.Forms.TextBox();
		this.textBox2 = new System.Windows.Forms.TextBox();
		this.textBox1 = new System.Windows.Forms.TextBox();
		this.label19 = new System.Windows.Forms.Label();
		this.label18 = new System.Windows.Forms.Label();
		this.label17 = new System.Windows.Forms.Label();
		this.label16 = new System.Windows.Forms.Label();
		this.label14 = new System.Windows.Forms.Label();
		this.label15 = new System.Windows.Forms.Label();
		this.label12 = new System.Windows.Forms.Label();
		this.label13 = new System.Windows.Forms.Label();
		this.label11 = new System.Windows.Forms.Label();
		this.label9 = new System.Windows.Forms.Label();
		this.label10 = new System.Windows.Forms.Label();
		this.label6 = new System.Windows.Forms.Label();
		this.label7 = new System.Windows.Forms.Label();
		this.label8 = new System.Windows.Forms.Label();
		this.label5 = new System.Windows.Forms.Label();
		this.label4 = new System.Windows.Forms.Label();
		this.label3 = new System.Windows.Forms.Label();
		this.label2 = new System.Windows.Forms.Label();
		this.label1 = new System.Windows.Forms.Label();
		this.tabPage3 = new System.Windows.Forms.TabPage();
		this.dataGridView1 = new System.Windows.Forms.DataGridView();
		this.iDDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.valueDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.descriptionDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.nameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.effectBindingSource = new System.Windows.Forms.BindingSource(this.components);
		this.tabPage2 = new System.Windows.Forms.TabPage();
		this.listBoxClothes = new System.Windows.Forms.ListBox();
		this.itemClothBindingSource = new System.Windows.Forms.BindingSource(this.components);
		this.label27 = new System.Windows.Forms.Label();
		this.textBoxXSDInfo = new System.Windows.Forms.TextBox();
		this.tabControl1.SuspendLayout();
		this.tabPage1.SuspendLayout();
		this.groupBox2.SuspendLayout();
		this.groupBox1.SuspendLayout();
		this.tabPage3.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.dataGridView1).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.effectBindingSource).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.itemClothBindingSource).BeginInit();
		base.SuspendLayout();
		this.comboBox1.Dock = System.Windows.Forms.DockStyle.Top;
		this.comboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBox1.FormattingEnabled = true;
		this.comboBox1.Items.AddRange(new object[3] { "Clothes", "Clothes2", "Clothes3" });
		this.comboBox1.Location = new System.Drawing.Point(0, 0);
		this.comboBox1.Name = "comboBox1";
		this.comboBox1.Size = new System.Drawing.Size(862, 21);
		this.comboBox1.TabIndex = 0;
		this.comboBox1.SelectedIndexChanged += new System.EventHandler(comboBox1_SelectedIndexChanged);
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPage3);
		this.tabControl1.Controls.Add(this.tabPage2);
		this.tabControl1.Location = new System.Drawing.Point(203, 26);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new System.Drawing.Size(650, 533);
		this.tabControl1.TabIndex = 0;
		this.tabPage1.Controls.Add(this.textBoxXSDInfo);
		this.tabPage1.Controls.Add(this.label27);
		this.tabPage1.Controls.Add(this.textBoxXSDIndex);
		this.tabPage1.Controls.Add(this.label26);
		this.tabPage1.Controls.Add(this.multipleItems);
		this.tabPage1.Controls.Add(this.btn_pasteToMultiple);
		this.tabPage1.Controls.Add(this.groupBox2);
		this.tabPage1.Controls.Add(this.groupBox1);
		this.tabPage1.Controls.Add(this.comboBoxNickname);
		this.tabPage1.Controls.Add(this.label25);
		this.tabPage1.Controls.Add(this.btn_deleteItem);
		this.tabPage1.Controls.Add(this.unknownByteValue);
		this.tabPage1.Controls.Add(this.unknownByteIndex);
		this.tabPage1.Controls.Add(this.label24);
		this.tabPage1.Controls.Add(this.textBoxTime);
		this.tabPage1.Controls.Add(this.label23);
		this.tabPage1.Controls.Add(this.checkBoxSellNPC);
		this.tabPage1.Controls.Add(this.checkBoxTrade);
		this.tabPage1.Controls.Add(this.checkBoxDrop);
		this.tabPage1.Controls.Add(this.textBox22);
		this.tabPage1.Controls.Add(this.label22);
		this.tabPage1.Controls.Add(this.textBox20);
		this.tabPage1.Controls.Add(this.textBox21);
		this.tabPage1.Controls.Add(this.label20);
		this.tabPage1.Controls.Add(this.label21);
		this.tabPage1.Controls.Add(this.lbLevelDisplay);
		this.tabPage1.Controls.Add(this.textBox19);
		this.tabPage1.Controls.Add(this.textBox18);
		this.tabPage1.Controls.Add(this.textBox17);
		this.tabPage1.Controls.Add(this.textBox16);
		this.tabPage1.Controls.Add(this.textBox15);
		this.tabPage1.Controls.Add(this.textBox14);
		this.tabPage1.Controls.Add(this.textBox13);
		this.tabPage1.Controls.Add(this.textBox12);
		this.tabPage1.Controls.Add(this.textBox11);
		this.tabPage1.Controls.Add(this.textBox10);
		this.tabPage1.Controls.Add(this.textBox9);
		this.tabPage1.Controls.Add(this.textBox8);
		this.tabPage1.Controls.Add(this.textBox7);
		this.tabPage1.Controls.Add(this.textBox6);
		this.tabPage1.Controls.Add(this.textBox5);
		this.tabPage1.Controls.Add(this.textBox4);
		this.tabPage1.Controls.Add(this.textBox3);
		this.tabPage1.Controls.Add(this.textBox2);
		this.tabPage1.Controls.Add(this.textBox1);
		this.tabPage1.Controls.Add(this.label19);
		this.tabPage1.Controls.Add(this.label18);
		this.tabPage1.Controls.Add(this.label17);
		this.tabPage1.Controls.Add(this.label16);
		this.tabPage1.Controls.Add(this.label14);
		this.tabPage1.Controls.Add(this.label15);
		this.tabPage1.Controls.Add(this.label12);
		this.tabPage1.Controls.Add(this.label13);
		this.tabPage1.Controls.Add(this.label11);
		this.tabPage1.Controls.Add(this.label9);
		this.tabPage1.Controls.Add(this.label10);
		this.tabPage1.Controls.Add(this.label6);
		this.tabPage1.Controls.Add(this.label7);
		this.tabPage1.Controls.Add(this.label8);
		this.tabPage1.Controls.Add(this.label5);
		this.tabPage1.Controls.Add(this.label4);
		this.tabPage1.Controls.Add(this.label3);
		this.tabPage1.Controls.Add(this.label2);
		this.tabPage1.Controls.Add(this.label1);
		this.tabPage1.Location = new System.Drawing.Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(642, 507);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "Details";
		this.tabPage1.UseVisualStyleBackColor = true;
		this.textBoxXSDIndex.Location = new System.Drawing.Point(388, 277);
		this.textBoxXSDIndex.Margin = new System.Windows.Forms.Padding(2);
		this.textBoxXSDIndex.Name = "textBoxXSDIndex";
		this.textBoxXSDIndex.Size = new System.Drawing.Size(76, 20);
		this.textBoxXSDIndex.TabIndex = 65;
		this.textBoxXSDIndex.TextChanged += new System.EventHandler(textBoxXSDIndex_TextChanged);
		this.label26.AutoSize = true;
		this.label26.Location = new System.Drawing.Point(314, 280);
		this.label26.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label26.Name = "label26";
		this.label26.Size = new System.Drawing.Size(58, 13);
		this.label26.TabIndex = 64;
		this.label26.Text = "XSD Index";
		this.multipleItems.Location = new System.Drawing.Point(154, 469);
		this.multipleItems.Name = "multipleItems";
		this.multipleItems.Size = new System.Drawing.Size(484, 20);
		this.multipleItems.TabIndex = 63;
		this.btn_pasteToMultiple.Location = new System.Drawing.Point(39, 461);
		this.btn_pasteToMultiple.Name = "btn_pasteToMultiple";
		this.btn_pasteToMultiple.Size = new System.Drawing.Size(105, 40);
		this.btn_pasteToMultiple.TabIndex = 62;
		this.btn_pasteToMultiple.Text = "Paste Effects To Multiple Items";
		this.btn_pasteToMultiple.UseVisualStyleBackColor = true;
		this.btn_pasteToMultiple.Click += new System.EventHandler(btn_pasteToMultiple_Click);
		this.groupBox2.Controls.Add(this.btn_pasteEffects);
		this.groupBox2.Controls.Add(this.btn_copyEffects);
		this.groupBox2.Location = new System.Drawing.Point(353, 417);
		this.groupBox2.Name = "groupBox2";
		this.groupBox2.Size = new System.Drawing.Size(284, 40);
		this.groupBox2.TabIndex = 61;
		this.groupBox2.TabStop = false;
		this.btn_pasteEffects.Enabled = false;
		this.btn_pasteEffects.Location = new System.Drawing.Point(152, 11);
		this.btn_pasteEffects.Name = "btn_pasteEffects";
		this.btn_pasteEffects.Size = new System.Drawing.Size(123, 23);
		this.btn_pasteEffects.TabIndex = 57;
		this.btn_pasteEffects.Text = "Paste Effects";
		this.btn_pasteEffects.UseVisualStyleBackColor = false;
		this.btn_pasteEffects.Click += new System.EventHandler(btn_pasteEffects_Click);
		this.btn_copyEffects.Location = new System.Drawing.Point(8, 11);
		this.btn_copyEffects.Name = "btn_copyEffects";
		this.btn_copyEffects.Size = new System.Drawing.Size(123, 23);
		this.btn_copyEffects.TabIndex = 56;
		this.btn_copyEffects.Text = "Copy Effects";
		this.btn_copyEffects.UseVisualStyleBackColor = false;
		this.btn_copyEffects.Click += new System.EventHandler(btn_copyEffects_Click);
		this.groupBox1.Controls.Add(this.btn_pasteItem);
		this.groupBox1.Controls.Add(this.btn_copyItem);
		this.groupBox1.Location = new System.Drawing.Point(353, 370);
		this.groupBox1.Name = "groupBox1";
		this.groupBox1.Size = new System.Drawing.Size(285, 39);
		this.groupBox1.TabIndex = 60;
		this.groupBox1.TabStop = false;
		this.btn_pasteItem.Enabled = false;
		this.btn_pasteItem.Location = new System.Drawing.Point(153, 10);
		this.btn_pasteItem.Name = "btn_pasteItem";
		this.btn_pasteItem.Size = new System.Drawing.Size(123, 23);
		this.btn_pasteItem.TabIndex = 55;
		this.btn_pasteItem.Text = "Paste Item";
		this.btn_pasteItem.UseVisualStyleBackColor = false;
		this.btn_pasteItem.Click += new System.EventHandler(btn_pasteItem_Click);
		this.btn_copyItem.Location = new System.Drawing.Point(8, 10);
		this.btn_copyItem.Name = "btn_copyItem";
		this.btn_copyItem.Size = new System.Drawing.Size(123, 23);
		this.btn_copyItem.TabIndex = 54;
		this.btn_copyItem.Text = "Copy Item";
		this.btn_copyItem.UseVisualStyleBackColor = false;
		this.btn_copyItem.Click += new System.EventHandler(btn_copyItem_Click);
		this.comboBoxNickname.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxNickname.FormattingEnabled = true;
		this.comboBoxNickname.Location = new System.Drawing.Point(141, 360);
		this.comboBoxNickname.Name = "comboBoxNickname";
		this.comboBoxNickname.Size = new System.Drawing.Size(121, 21);
		this.comboBoxNickname.TabIndex = 59;
		this.comboBoxNickname.SelectedIndexChanged += new System.EventHandler(comboBoxNickname_SelectedIndexChanged);
		this.label25.AutoSize = true;
		this.label25.Location = new System.Drawing.Point(37, 363);
		this.label25.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label25.Name = "label25";
		this.label25.Size = new System.Drawing.Size(55, 13);
		this.label25.TabIndex = 58;
		this.label25.Text = "Nickname";
		this.btn_deleteItem.Location = new System.Drawing.Point(40, 397);
		this.btn_deleteItem.Name = "btn_deleteItem";
		this.btn_deleteItem.Size = new System.Drawing.Size(123, 23);
		this.btn_deleteItem.TabIndex = 53;
		this.btn_deleteItem.Text = "Delete Item";
		this.btn_deleteItem.UseVisualStyleBackColor = false;
		this.btn_deleteItem.Click += new System.EventHandler(btn_deleteItem_Click);
		this.unknownByteValue.Location = new System.Drawing.Point(478, 245);
		this.unknownByteValue.Margin = new System.Windows.Forms.Padding(2);
		this.unknownByteValue.Name = "unknownByteValue";
		this.unknownByteValue.Size = new System.Drawing.Size(52, 20);
		this.unknownByteValue.TabIndex = 52;
		this.unknownByteValue.TextChanged += new System.EventHandler(unknownByteValue_TextChanged);
		this.unknownByteIndex.FormattingEnabled = true;
		this.unknownByteIndex.Location = new System.Drawing.Point(417, 245);
		this.unknownByteIndex.Name = "unknownByteIndex";
		this.unknownByteIndex.Size = new System.Drawing.Size(47, 21);
		this.unknownByteIndex.TabIndex = 51;
		this.unknownByteIndex.SelectedIndexChanged += new System.EventHandler(unknownByteIndex_SelectedIndexChanged);
		this.label24.AutoSize = true;
		this.label24.Location = new System.Drawing.Point(306, 249);
		this.label24.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label24.Name = "label24";
		this.label24.Size = new System.Drawing.Size(82, 13);
		this.label24.TabIndex = 50;
		this.label24.Text = "Unknown Bytes";
		this.textBoxTime.Location = new System.Drawing.Point(141, 335);
		this.textBoxTime.Margin = new System.Windows.Forms.Padding(2);
		this.textBoxTime.Name = "textBoxTime";
		this.textBoxTime.Size = new System.Drawing.Size(76, 20);
		this.textBoxTime.TabIndex = 49;
		this.textBoxTime.TextChanged += new System.EventHandler(textBoxTime_TextChanged);
		this.label23.AutoSize = true;
		this.label23.Location = new System.Drawing.Point(36, 337);
		this.label23.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label23.Name = "label23";
		this.label23.Size = new System.Drawing.Size(30, 13);
		this.label23.TabIndex = 48;
		this.label23.Text = "Time";
		this.checkBoxSellNPC.AutoSize = true;
		this.checkBoxSellNPC.Location = new System.Drawing.Point(308, 352);
		this.checkBoxSellNPC.Name = "checkBoxSellNPC";
		this.checkBoxSellNPC.Size = new System.Drawing.Size(105, 17);
		this.checkBoxSellNPC.TabIndex = 47;
		this.checkBoxSellNPC.Text = "Can't sell to NPC";
		this.checkBoxSellNPC.UseVisualStyleBackColor = true;
		this.checkBoxSellNPC.CheckedChanged += new System.EventHandler(checkBoxSellNPC_CheckedChanged);
		this.checkBoxTrade.AutoSize = true;
		this.checkBoxTrade.Location = new System.Drawing.Point(308, 329);
		this.checkBoxTrade.Name = "checkBoxTrade";
		this.checkBoxTrade.Size = new System.Drawing.Size(77, 17);
		this.checkBoxTrade.TabIndex = 46;
		this.checkBoxTrade.Text = "Can't trade";
		this.checkBoxTrade.ThreeState = true;
		this.checkBoxTrade.UseVisualStyleBackColor = true;
		this.checkBoxTrade.CheckStateChanged += new System.EventHandler(checkBoxTrade_CheckStateChanged);
		this.checkBoxDrop.AutoSize = true;
		this.checkBoxDrop.Location = new System.Drawing.Point(308, 305);
		this.checkBoxDrop.Name = "checkBoxDrop";
		this.checkBoxDrop.Size = new System.Drawing.Size(87, 17);
		this.checkBoxDrop.TabIndex = 45;
		this.checkBoxDrop.Text = "Can't discard";
		this.checkBoxDrop.UseVisualStyleBackColor = true;
		this.checkBoxDrop.CheckedChanged += new System.EventHandler(checkBoxDrop_CheckedChanged);
		this.textBox22.Location = new System.Drawing.Point(388, 216);
		this.textBox22.Margin = new System.Windows.Forms.Padding(2);
		this.textBox22.Name = "textBox22";
		this.textBox22.Size = new System.Drawing.Size(76, 20);
		this.textBox22.TabIndex = 44;
		this.textBox22.TextChanged += new System.EventHandler(textBox22_TextChanged);
		this.label22.AutoSize = true;
		this.label22.Location = new System.Drawing.Point(305, 216);
		this.label22.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label22.Name = "label22";
		this.label22.Size = new System.Drawing.Size(46, 13);
		this.label22.TabIndex = 43;
		this.label22.Text = "Pockets";
		this.textBox20.Location = new System.Drawing.Point(388, 167);
		this.textBox20.Margin = new System.Windows.Forms.Padding(2);
		this.textBox20.Name = "textBox20";
		this.textBox20.Size = new System.Drawing.Size(76, 20);
		this.textBox20.TabIndex = 42;
		this.textBox20.TextChanged += new System.EventHandler(textBox20_TextChanged);
		this.textBox21.Location = new System.Drawing.Point(388, 192);
		this.textBox21.Margin = new System.Windows.Forms.Padding(2);
		this.textBox21.Name = "textBox21";
		this.textBox21.Size = new System.Drawing.Size(76, 20);
		this.textBox21.TabIndex = 41;
		this.textBox21.TextChanged += new System.EventHandler(textBox21_TextChanged);
		this.label20.AutoSize = true;
		this.label20.Location = new System.Drawing.Point(305, 171);
		this.label20.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label20.Name = "label20";
		this.label20.Size = new System.Drawing.Size(67, 13);
		this.label20.TabIndex = 40;
		this.label20.Text = "Current Slots";
		this.label21.AutoSize = true;
		this.label21.Location = new System.Drawing.Point(305, 192);
		this.label21.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label21.Name = "label21";
		this.label21.Size = new System.Drawing.Size(53, 13);
		this.label21.TabIndex = 39;
		this.label21.Text = "Max Slots";
		this.lbLevelDisplay.AutoSize = true;
		this.lbLevelDisplay.Location = new System.Drawing.Point(480, 98);
		this.lbLevelDisplay.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.lbLevelDisplay.Name = "lbLevelDisplay";
		this.lbLevelDisplay.Size = new System.Drawing.Size(70, 13);
		this.lbLevelDisplay.TabIndex = 38;
		this.lbLevelDisplay.Text = "Level Display";
		this.textBox19.Location = new System.Drawing.Point(141, 311);
		this.textBox19.Margin = new System.Windows.Forms.Padding(2);
		this.textBox19.Name = "textBox19";
		this.textBox19.Size = new System.Drawing.Size(76, 20);
		this.textBox19.TabIndex = 37;
		this.textBox19.TextChanged += new System.EventHandler(textBox19_TextChanged);
		this.textBox18.Location = new System.Drawing.Point(388, 141);
		this.textBox18.Margin = new System.Windows.Forms.Padding(2);
		this.textBox18.Name = "textBox18";
		this.textBox18.Size = new System.Drawing.Size(76, 20);
		this.textBox18.TabIndex = 36;
		this.textBox18.TextChanged += new System.EventHandler(textBox18_TextChanged);
		this.textBox17.Location = new System.Drawing.Point(388, 119);
		this.textBox17.Margin = new System.Windows.Forms.Padding(2);
		this.textBox17.Name = "textBox17";
		this.textBox17.Size = new System.Drawing.Size(76, 20);
		this.textBox17.TabIndex = 35;
		this.textBox17.TextChanged += new System.EventHandler(textBox17_TextChanged);
		this.textBox16.Location = new System.Drawing.Point(388, 96);
		this.textBox16.Margin = new System.Windows.Forms.Padding(2);
		this.textBox16.Name = "textBox16";
		this.textBox16.Size = new System.Drawing.Size(76, 20);
		this.textBox16.TabIndex = 34;
		this.textBox16.TextChanged += new System.EventHandler(textBox16_TextChanged);
		this.textBox15.Location = new System.Drawing.Point(388, 72);
		this.textBox15.Margin = new System.Windows.Forms.Padding(2);
		this.textBox15.Name = "textBox15";
		this.textBox15.Size = new System.Drawing.Size(76, 20);
		this.textBox15.TabIndex = 33;
		this.textBox15.TextChanged += new System.EventHandler(textBox15_TextChanged);
		this.textBox14.Location = new System.Drawing.Point(388, 49);
		this.textBox14.Margin = new System.Windows.Forms.Padding(2);
		this.textBox14.Name = "textBox14";
		this.textBox14.Size = new System.Drawing.Size(76, 20);
		this.textBox14.TabIndex = 32;
		this.textBox14.TextChanged += new System.EventHandler(textBox14_TextChanged);
		this.textBox13.Location = new System.Drawing.Point(388, 25);
		this.textBox13.Margin = new System.Windows.Forms.Padding(2);
		this.textBox13.Name = "textBox13";
		this.textBox13.Size = new System.Drawing.Size(76, 20);
		this.textBox13.TabIndex = 31;
		this.textBox13.TextChanged += new System.EventHandler(textBox13_TextChanged);
		this.textBox12.Location = new System.Drawing.Point(141, 287);
		this.textBox12.Margin = new System.Windows.Forms.Padding(2);
		this.textBox12.Name = "textBox12";
		this.textBox12.Size = new System.Drawing.Size(76, 20);
		this.textBox12.TabIndex = 30;
		this.textBox12.TextChanged += new System.EventHandler(textBox12_TextChanged);
		this.textBox11.Location = new System.Drawing.Point(141, 263);
		this.textBox11.Margin = new System.Windows.Forms.Padding(2);
		this.textBox11.Name = "textBox11";
		this.textBox11.Size = new System.Drawing.Size(76, 20);
		this.textBox11.TabIndex = 29;
		this.textBox11.TextChanged += new System.EventHandler(textBox11_TextChanged);
		this.textBox10.Location = new System.Drawing.Point(141, 239);
		this.textBox10.Margin = new System.Windows.Forms.Padding(2);
		this.textBox10.Name = "textBox10";
		this.textBox10.Size = new System.Drawing.Size(76, 20);
		this.textBox10.TabIndex = 28;
		this.textBox10.TextChanged += new System.EventHandler(textBox10_TextChanged);
		this.textBox9.Location = new System.Drawing.Point(141, 215);
		this.textBox9.Margin = new System.Windows.Forms.Padding(2);
		this.textBox9.Name = "textBox9";
		this.textBox9.Size = new System.Drawing.Size(76, 20);
		this.textBox9.TabIndex = 27;
		this.textBox9.TextChanged += new System.EventHandler(textBox9_TextChanged);
		this.textBox8.Location = new System.Drawing.Point(141, 191);
		this.textBox8.Margin = new System.Windows.Forms.Padding(2);
		this.textBox8.Name = "textBox8";
		this.textBox8.Size = new System.Drawing.Size(76, 20);
		this.textBox8.TabIndex = 26;
		this.textBox8.TextChanged += new System.EventHandler(textBox8_TextChanged);
		this.textBox7.Location = new System.Drawing.Point(141, 167);
		this.textBox7.Margin = new System.Windows.Forms.Padding(2);
		this.textBox7.Name = "textBox7";
		this.textBox7.Size = new System.Drawing.Size(76, 20);
		this.textBox7.TabIndex = 25;
		this.textBox7.TextChanged += new System.EventHandler(textBox7_TextChanged);
		this.textBox6.Location = new System.Drawing.Point(141, 143);
		this.textBox6.Margin = new System.Windows.Forms.Padding(2);
		this.textBox6.Name = "textBox6";
		this.textBox6.Size = new System.Drawing.Size(76, 20);
		this.textBox6.TabIndex = 24;
		this.textBox6.TextChanged += new System.EventHandler(textBox6_TextChanged);
		this.textBox5.Location = new System.Drawing.Point(141, 119);
		this.textBox5.Margin = new System.Windows.Forms.Padding(2);
		this.textBox5.Name = "textBox5";
		this.textBox5.Size = new System.Drawing.Size(76, 20);
		this.textBox5.TabIndex = 23;
		this.textBox5.TextChanged += new System.EventHandler(textBox5_TextChanged);
		this.textBox4.Location = new System.Drawing.Point(141, 95);
		this.textBox4.Margin = new System.Windows.Forms.Padding(2);
		this.textBox4.Name = "textBox4";
		this.textBox4.Size = new System.Drawing.Size(76, 20);
		this.textBox4.TabIndex = 22;
		this.textBox4.TextChanged += new System.EventHandler(textBox4_TextChanged);
		this.textBox3.Location = new System.Drawing.Point(141, 71);
		this.textBox3.Margin = new System.Windows.Forms.Padding(2);
		this.textBox3.Name = "textBox3";
		this.textBox3.Size = new System.Drawing.Size(76, 20);
		this.textBox3.TabIndex = 21;
		this.textBox3.TextChanged += new System.EventHandler(textBox3_TextChanged);
		this.textBox2.Location = new System.Drawing.Point(141, 47);
		this.textBox2.Margin = new System.Windows.Forms.Padding(2);
		this.textBox2.Name = "textBox2";
		this.textBox2.Size = new System.Drawing.Size(76, 20);
		this.textBox2.TabIndex = 20;
		this.textBox2.TextChanged += new System.EventHandler(textBox2_TextChanged);
		this.textBox1.Location = new System.Drawing.Point(141, 23);
		this.textBox1.Margin = new System.Windows.Forms.Padding(2);
		this.textBox1.Name = "textBox1";
		this.textBox1.Size = new System.Drawing.Size(76, 20);
		this.textBox1.TabIndex = 19;
		this.textBox1.TextChanged += new System.EventHandler(textBox1_TextChanged);
		this.label19.AutoSize = true;
		this.label19.Location = new System.Drawing.Point(305, 145);
		this.label19.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label19.Name = "label19";
		this.label19.Size = new System.Drawing.Size(31, 13);
		this.label19.TabIndex = 18;
		this.label19.Text = "Price";
		this.label18.AutoSize = true;
		this.label18.Location = new System.Drawing.Point(36, 289);
		this.label18.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label18.Name = "label18";
		this.label18.Size = new System.Drawing.Size(54, 13);
		this.label18.TabIndex = 17;
		this.label18.Text = "Item Case";
		this.label17.AutoSize = true;
		this.label17.Location = new System.Drawing.Point(36, 313);
		this.label17.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label17.Name = "label17";
		this.label17.Size = new System.Drawing.Size(56, 13);
		this.label17.TabIndex = 16;
		this.label17.Text = "Item Rank";
		this.label16.AutoSize = true;
		this.label16.Location = new System.Drawing.Point(305, 72);
		this.label16.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label16.Name = "label16";
		this.label16.Size = new System.Drawing.Size(46, 13);
		this.label16.TabIndex = 15;
		this.label16.Text = "Item Set";
		this.label14.AutoSize = true;
		this.label14.Location = new System.Drawing.Point(305, 49);
		this.label14.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label14.Name = "label14";
		this.label14.Size = new System.Drawing.Size(50, 13);
		this.label14.TabIndex = 14;
		this.label14.Text = "Durability";
		this.label15.AutoSize = true;
		this.label15.Location = new System.Drawing.Point(305, 25);
		this.label15.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label15.Name = "label15";
		this.label15.Size = new System.Drawing.Size(47, 13);
		this.label15.TabIndex = 13;
		this.label15.Text = "Defense";
		this.label12.AutoSize = true;
		this.label12.Location = new System.Drawing.Point(36, 265);
		this.label12.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label12.Name = "label12";
		this.label12.Size = new System.Drawing.Size(96, 13);
		this.label12.TabIndex = 12;
		this.label12.Text = "Clan Contribution 2";
		this.label13.AutoSize = true;
		this.label13.Location = new System.Drawing.Point(36, 241);
		this.label13.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label13.Name = "label13";
		this.label13.Size = new System.Drawing.Size(96, 13);
		this.label13.TabIndex = 11;
		this.label13.Text = "Clan Contribution 1";
		this.label11.AutoSize = true;
		this.label11.Location = new System.Drawing.Point(36, 145);
		this.label11.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label11.Name = "label11";
		this.label11.Size = new System.Drawing.Size(57, 13);
		this.label11.TabIndex = 10;
		this.label11.Text = "Apply Clan";
		this.label9.AutoSize = true;
		this.label9.Location = new System.Drawing.Point(36, 121);
		this.label9.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label9.Name = "label9";
		this.label9.Size = new System.Drawing.Size(25, 13);
		this.label9.TabIndex = 9;
		this.label9.Text = "Sex";
		this.label10.AutoSize = true;
		this.label10.Location = new System.Drawing.Point(36, 97);
		this.label10.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label10.Name = "label10";
		this.label10.Size = new System.Drawing.Size(28, 13);
		this.label10.TabIndex = 8;
		this.label10.Text = "Clan";
		this.label6.AutoSize = true;
		this.label6.Location = new System.Drawing.Point(36, 73);
		this.label6.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label6.Name = "label6";
		this.label6.Size = new System.Drawing.Size(65, 13);
		this.label6.TabIndex = 7;
		this.label6.Text = "Model Index";
		this.label7.AutoSize = true;
		this.label7.Location = new System.Drawing.Point(36, 49);
		this.label7.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(52, 13);
		this.label7.TabIndex = 6;
		this.label7.Text = "2nd Type";
		this.label8.AutoSize = true;
		this.label8.Location = new System.Drawing.Point(36, 25);
		this.label8.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(31, 13);
		this.label8.TabIndex = 5;
		this.label8.Text = "Type";
		this.label5.AutoSize = true;
		this.label5.Location = new System.Drawing.Point(305, 119);
		this.label5.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(36, 13);
		this.label5.TabIndex = 4;
		this.label5.Text = "Grade";
		this.label4.AutoSize = true;
		this.label4.Location = new System.Drawing.Point(36, 217);
		this.label4.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(65, 13);
		this.label4.TabIndex = 3;
		this.label4.Text = "Precedence";
		this.label3.AutoSize = true;
		this.label3.Location = new System.Drawing.Point(36, 193);
		this.label3.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(57, 13);
		this.label3.TabIndex = 2;
		this.label3.Text = "Icon Index";
		this.label2.AutoSize = true;
		this.label2.Location = new System.Drawing.Point(36, 169);
		this.label2.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(85, 13);
		this.label2.TabIndex = 1;
		this.label2.Text = "Character Grade";
		this.label1.AutoSize = true;
		this.label1.Location = new System.Drawing.Point(305, 96);
		this.label1.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(33, 13);
		this.label1.TabIndex = 0;
		this.label1.Text = "Level";
		this.tabPage3.Controls.Add(this.dataGridView1);
		this.tabPage3.Location = new System.Drawing.Point(4, 22);
		this.tabPage3.Name = "tabPage3";
		this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage3.Size = new System.Drawing.Size(642, 507);
		this.tabPage3.TabIndex = 2;
		this.tabPage3.Text = "Eff Edit";
		this.tabPage3.UseVisualStyleBackColor = true;
		this.dataGridView1.AllowUserToAddRows = false;
		this.dataGridView1.AllowUserToDeleteRows = false;
		this.dataGridView1.AutoGenerateColumns = false;
		this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.dataGridView1.Columns.AddRange(this.iDDataGridViewTextBoxColumn, this.valueDataGridViewTextBoxColumn, this.descriptionDataGridViewTextBoxColumn, this.nameDataGridViewTextBoxColumn);
		this.dataGridView1.DataSource = this.effectBindingSource;
		this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.dataGridView1.Location = new System.Drawing.Point(3, 3);
		this.dataGridView1.Name = "dataGridView1";
		this.dataGridView1.Size = new System.Drawing.Size(636, 501);
		this.dataGridView1.TabIndex = 0;
		this.dataGridView1.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(dataGridView1_CellValueChanged);
		this.iDDataGridViewTextBoxColumn.DataPropertyName = "ID";
		this.iDDataGridViewTextBoxColumn.HeaderText = "ID";
		this.iDDataGridViewTextBoxColumn.Name = "iDDataGridViewTextBoxColumn";
		this.valueDataGridViewTextBoxColumn.DataPropertyName = "Value";
		this.valueDataGridViewTextBoxColumn.HeaderText = "Value";
		this.valueDataGridViewTextBoxColumn.Name = "valueDataGridViewTextBoxColumn";
		this.descriptionDataGridViewTextBoxColumn.DataPropertyName = "Description";
		this.descriptionDataGridViewTextBoxColumn.HeaderText = "Description";
		this.descriptionDataGridViewTextBoxColumn.Name = "descriptionDataGridViewTextBoxColumn";
		this.descriptionDataGridViewTextBoxColumn.ReadOnly = true;
		this.nameDataGridViewTextBoxColumn.DataPropertyName = "Name";
		this.nameDataGridViewTextBoxColumn.HeaderText = "Name";
		this.nameDataGridViewTextBoxColumn.Name = "nameDataGridViewTextBoxColumn";
		this.effectBindingSource.DataSource = typeof(ItemTableReader.Effect);
		this.tabPage2.Location = new System.Drawing.Point(4, 22);
		this.tabPage2.Name = "tabPage2";
		this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage2.Size = new System.Drawing.Size(642, 507);
		this.tabPage2.TabIndex = 1;
		this.tabPage2.Text = "Unknown bytes";
		this.tabPage2.UseVisualStyleBackColor = true;
		this.listBoxClothes.FormattingEnabled = true;
		this.listBoxClothes.Location = new System.Drawing.Point(8, 26);
		this.listBoxClothes.Name = "listBoxClothes";
		this.listBoxClothes.Size = new System.Drawing.Size(190, 407);
		this.listBoxClothes.TabIndex = 0;
		this.listBoxClothes.SelectedIndexChanged += new System.EventHandler(listBoxClothes_SelectedIndexChanged);
		this.itemClothBindingSource.DataSource = typeof(ItemTableReader.ItemCloth);
		this.label27.AutoSize = true;
		this.label27.Location = new System.Drawing.Point(475, 280);
		this.label27.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label27.Name = "label27";
		this.label27.Size = new System.Drawing.Size(50, 13);
		this.label27.TabIndex = 66;
		this.label27.Text = "XSD Info";
		this.textBoxXSDInfo.Location = new System.Drawing.Point(537, 277);
		this.textBoxXSDInfo.Margin = new System.Windows.Forms.Padding(2);
		this.textBoxXSDInfo.Name = "textBoxXSDInfo";
		this.textBoxXSDInfo.Size = new System.Drawing.Size(76, 20);
		this.textBoxXSDInfo.TabIndex = 67;
		this.textBoxXSDInfo.TextChanged += new System.EventHandler(textBoxXSDInfo_TextChanged);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(862, 571);
		base.Controls.Add(this.tabControl1);
		base.Controls.Add(this.listBoxClothes);
		base.Controls.Add(this.comboBox1);
		base.MaximizeBox = false;
		base.MinimizeBox = false;
		base.Name = "FormClothes";
		base.ShowIcon = false;
		this.Text = "Clothes Editor";
		base.Load += new System.EventHandler(FormClothes_Load);
		base.KeyDown += new System.Windows.Forms.KeyEventHandler(FormClothes_KeyDown);
		this.tabControl1.ResumeLayout(false);
		this.tabPage1.ResumeLayout(false);
		this.tabPage1.PerformLayout();
		this.groupBox2.ResumeLayout(false);
		this.groupBox1.ResumeLayout(false);
		this.tabPage3.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.dataGridView1).EndInit();
		((System.ComponentModel.ISupportInitialize)this.effectBindingSource).EndInit();
		((System.ComponentModel.ISupportInitialize)this.itemClothBindingSource).EndInit();
		base.ResumeLayout(false);
	}
}
