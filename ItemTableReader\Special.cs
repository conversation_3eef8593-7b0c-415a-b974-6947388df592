using System.IO;

namespace ItemTableReader;

internal class Special
{
	internal ushort id;

	internal ushort time;

	internal byte value;

	internal byte type;

	internal ushort prob;

	public ushort ID
	{
		get
		{
			return id;
		}
		set
		{
			id = value;
		}
	}

	public byte Value
	{
		get
		{
			return value;
		}
		set
		{
			this.value = value;
		}
	}

	public ushort Time
	{
		get
		{
			return time;
		}
		set
		{
			time = value;
		}
	}

	public byte Type
	{
		get
		{
			return type;
		}
		set
		{
			type = value;
		}
	}

	public ushort Prob
	{
		get
		{
			return prob;
		}
		set
		{
			prob = value;
		}
	}

	public Special()
	{
	}

	public Special(Special s)
	{
		MemberwiseClone();
	}

	public void Load(byte[] s)
	{
		id = (ushort)((s[1] << 8) | s[0]);
		time = (ushort)((s[3] << 8) | s[2]);
		value = s[4];
		type = s[5];
		prob = (ushort)((s[7] << 8) | s[6]);
	}

	public void Save(Stream s)
	{
		BinaryWriter binaryWriter = new BinaryWriter(s);
		binaryWriter.Write(id);
		binaryWriter.Write(time);
		binaryWriter.Write(value);
		binaryWriter.Write(type);
		binaryWriter.Write(prob);
	}
}
