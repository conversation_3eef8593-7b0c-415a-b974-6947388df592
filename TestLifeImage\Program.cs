using System;
using System.Drawing;
using System.IO;

namespace TestLifeImage
{
    class Program
    {
        static void Main()
        {
            try
            {
                string imagePath = @"..\Resources\Life.png";
            if (File.Exists(imagePath))
            {
                using (Bitmap image = new Bitmap(imagePath))
                {
                    Console.WriteLine($"Life.png found!");
                    Console.WriteLine($"Width: {image.Width}");
                    Console.WriteLine($"Height: {image.Height}");
                    Console.WriteLine($"PixelFormat: {image.PixelFormat}");
                    
                    // 计算可能的图标数量
                    int iconsPerRow = image.Width / 32;
                    int iconRows = image.Height / 32;
                    int totalIcons = iconsPerRow * iconRows;
                    
                    Console.WriteLine($"Icons per row (32px): {iconsPerRow}");
                    Console.WriteLine($"Icon rows: {iconRows}");
                    Console.WriteLine($"Total possible icons: {totalIcons}");
                    
                    // 测试切割第7个图标 (IconIndex=7, 转换为0-based是6)
                    int iconIndex = 6; // IconIndex 7 转换为 0-based
                    int x = (iconIndex % iconsPerRow) * 32;
                    int y = (iconIndex / iconsPerRow) * 32;
                    
                    Console.WriteLine($"For IconIndex 7 (0-based: 6):");
                    Console.WriteLine($"X position: {x}");
                    Console.WriteLine($"Y position: {y}");
                    
                    if (x + 32 <= image.Width && y + 32 <= image.Height)
                    {
                        Console.WriteLine("Icon position is within image bounds");
                        
                        // 尝试切割图标
                        Rectangle cropRect = new Rectangle(x, y, 32, 32);
                        using (Bitmap croppedIcon = new Bitmap(32, 32))
                        {
                            using (Graphics g = Graphics.FromImage(croppedIcon))
                            {
                                g.DrawImage(image, new Rectangle(0, 0, 32, 32), cropRect, GraphicsUnit.Pixel);
                            }
                            
                            // 保存切割的图标用于检查
                            croppedIcon.Save("test_life_icon_7.png");
                            Console.WriteLine("Saved cropped icon as test_life_icon_7.png");
                        }
                    }
                    else
                    {
                        Console.WriteLine("Icon position is outside image bounds!");
                    }
                }
            }
            else
            {
                Console.WriteLine("Life.png not found!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
    }
}
