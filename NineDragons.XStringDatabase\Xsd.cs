using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace NineDragons.XStringDatabase;

public abstract class Xsd
{
	public enum MergeStatus
	{
		Success,
		Failure
	}

	public enum MergeType
	{
		MatchingOnly
	}

	public class MergeResult
	{
		public string message;

		public MergeStatus status;

		public MergeResult(MergeStatus s)
		{
			status = s;
		}
	}

	public enum Language
	{
		Korean,
		Vietnamese,
		Taiwanese,
		English,
		Unknown1,
		Chinese,
		Russian,
		Unknown2,
		Thai,
		French,
		German
	}

	public enum Version
	{
		Version1 = 1,
		Separated = 65281,
		Version2 = 2
	}

	public SectionCollection sectionCollection = new SectionCollection();

	public static readonly IList<int> ValidHeaders = new ReadOnlyCollection<int>(new List<int> { 65487 });

	public int header;

	public int version;

	public KeyValuePair<int, string>[] languages = new KeyValuePair<int, string>[1];

	public int MaxLanguages { get; set; }

	public int totalSectionCount { get; set; }

	public string Filename { get; set; }

	public byte[] Keys { get; set; }

	public bool isEncrypted { get; set; }

	public event LoadedEventHandler Loaded;

	protected Xsd()
	{
	}

	protected Xsd(Version version)
		: this()
	{
		header = ValidHeaders[0];
		this.version = (int)version;
		setupLanguages();
	}

	protected void BindEvents()
	{
		sectionCollection.PropertyChanged += PropertyChangedEventHandler;
	}

	protected void setupLanguages()
	{
		switch ((Version)version)
		{
		case Version.Separated:
			MaxLanguages = 0;
			break;
		case Version.Version1:
			MaxLanguages = 5;
			break;
		case Version.Version2:
			MaxLanguages = 11;
			break;
		}
		if (MaxLanguages == 0)
		{
			languages[0] = new KeyValuePair<int, string>(3, Enum.GetName(typeof(Language), Language.English));
			return;
		}
		languages = new KeyValuePair<int, string>[MaxLanguages];
		for (int i = 0; i < MaxLanguages; i++)
		{
			languages[i] = new KeyValuePair<int, string>(i, Enum.GetName(typeof(Language), i));
		}
	}

	protected void PropertyChangedEventHandler(object sender, PropertyChangedEventArgs e)
	{
		if (e.PropertyName == "Sections")
		{
			totalSectionCount = sectionCollection.Sections.Count;
		}
	}

	protected virtual void OnLoaded(EventArgs e)
	{
		if (this.Loaded != null)
		{
			this.Loaded(this, e);
		}
	}

	public void load()
	{
		load(Filename);
	}

	public abstract void load(string filename);

	public void write(bool withEncryption = false)
	{
		write(Filename, withEncryption);
	}

	public abstract void write(string filename, bool withEncryption = false);

	public MergeResult Merge(Xsd source, MergeType type)
	{
		if (type == MergeType.MatchingOnly)
		{
			try
			{
				foreach (Section section in source.sectionCollection.Sections)
				{
					foreach (Section section2 in sectionCollection.Sections)
					{
						if (!section2.NameEqualsTo(section.Name))
						{
							continue;
						}
						foreach (XString row in section.XStrings.Rows)
						{
							foreach (XString row2 in section2.XStrings.Rows)
							{
								if (row2.ResourceIndex == row.ResourceIndex)
								{
									row2.TextString = row.TextString;
									row2.TextStringLength = row.TextStringLength;
									row2.ParameterOrder = row.ParameterOrder;
								}
							}
						}
					}
				}
			}
			catch (System.Exception)
			{
				return new MergeResult(MergeStatus.Failure);
			}
		}
		return new MergeResult(MergeStatus.Success);
	}
}
